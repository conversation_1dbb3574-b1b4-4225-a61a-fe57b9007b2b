package com.facishare.crm.fmcg.mengniu.api;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

public interface SignInGoodsRewardInvoke {

    @Data
    @ToString
    class Arg implements Serializable {

        @J<PERSON>NField(name = "tenant_id")
        @JsonProperty(value = "tenant_id")
        @SerializedName("tenant_id")
        private String tenantId;

        @JSONField(name = "delivery_note_id")
        @JsonProperty(value = "delivery_note_id")
        @SerializedName("delivery_note_id")
        private String deliveryNoteId;
    }

    @Data
    @ToString
    @Builder
    class Result implements Serializable {

        private String __placeholder;
    }
}