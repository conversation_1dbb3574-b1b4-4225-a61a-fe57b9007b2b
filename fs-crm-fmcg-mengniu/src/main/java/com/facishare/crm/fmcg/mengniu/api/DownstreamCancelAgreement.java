package com.facishare.crm.fmcg.mengniu.api;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * author: wuyx
 * description:
 * createTime: 2023/11/6 15:56
 */

public interface DownstreamCancelAgreement {

    @Data
    @ToString
    class Arg implements Serializable {

        @JSONField(name = "_id")
        @JsonProperty(value = "_id")
        @SerializedName("_id")
        private String id;

        @JSONField(name = "activity_agreement_id_list")
        @JsonProperty(value = "activity_agreement_id_list")
        @SerializedName("activity_agreement_id_list")
        private List<String> activityAgreementIdList;
    }

    @Data
    @ToString
    @Builder
    class Result implements Serializable {

        @JSONField(name = "data")
        @JsonProperty(value = "data")
        @SerializedName("data")
        private List<ObjectDataDocument> data;
    }
}