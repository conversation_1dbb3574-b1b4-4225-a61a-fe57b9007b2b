package com.facishare.crm.fmcg.mengniu.service;

import com.facishare.crm.fmcg.mengniu.api.MengNiuRewardRetry;
import com.facishare.crm.fmcg.mengniu.dto.RewardRetryEvent;
import com.facishare.crm.fmcg.mengniu.handler.RewardRetryHandler;
import com.facishare.paas.appframework.core.exception.AppBusinessException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

//IgnoreI18nFile
@Service
public class RewardRetryService implements IRewardRetryService {

    @Resource
    private RewardRetryHandler rewardRetryHandler;

    @Override
    public MengNiuRewardRetry.Result retry(MengNiuRewardRetry.Arg arg) {
        try {
            rewardRetryHandler.invoke(RewardRetryEvent.builder().tenantId(arg.getTenantId()).id(arg.getId()).build());
        } catch (AppBusinessException ex) {
            return MengNiuRewardRetry.Result.builder().success(false).code(ex.getErrorCode()).message(ex.getMessage()).build();
        } catch (Exception ex) {
            return MengNiuRewardRetry.Result.builder().success(false).code(500).message("发生未知异常，请稍后重试。").build();
        }
        return MengNiuRewardRetry.Result.builder().success(true).code(0).message("").build();
    }
}
