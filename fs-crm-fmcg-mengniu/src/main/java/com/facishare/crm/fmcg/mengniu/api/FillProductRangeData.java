package com.facishare.crm.fmcg.mengniu.api;

import lombok.Builder;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

public interface FillProductRangeData {

    @Data
    @ToString
    class Arg implements Serializable {

        private String tenantId;

        private String apiName;

        private Map<String, String> fields;
    }

    @Data
    @ToString
    @Builder
    class Result implements Serializable {

        private String data;
    }
}