package com.facishare.crm.fmcg.mengniu.api;

import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import lombok.Builder;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

public interface AgreementConfirm {

    @Data
    @ToString
    class Arg implements Serializable {

        private String id;

        private String signature;
    }

    @Data
    @ToString
    @Builder
    class Result implements Serializable {

        private ObjectDataDocument data;
    }
}