package com.facishare.crm.fmcg.ocr.analysis.mark;

import com.facishare.crm.fmcg.ocr.analysis.TextBlock;
import com.facishare.crm.fmcg.ocr.analysis.TextType;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.google.common.base.Strings;
import com.google.common.collect.Sets;
import org.apache.commons.io.FileUtils;
import org.springframework.util.ResourceUtils;

import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2023/2/22 10:49
 */
//IgnoreI18nFile
@SuppressWarnings("Duplicates")
public class PriceBlockMarker implements IBlockMarker {

    public static final BigDecimal MIN_PRICE = new BigDecimal("70");
    public static final BigDecimal MAX_PRICE = new BigDecimal("1000000");
    protected static final Set<Pattern> REGEX_LIST = Sets.newHashSet();
    protected static final Set<String> BLACK_LIST = Sets.newHashSet();

    protected static final Set<String> PRE_TEXT_END_WITH_WHITE_LIST = Sets.newHashSet("元", "RMB", "¥", "￥");
    protected static final Set<String> AFTER_TEXT_START_WITH_WHITE_LIST = Sets.newHashSet("元", "RMB", "¥", "￥");

    protected static final Set<String> PRE_TEXT_BLACK_LIST = Sets.newHashSet("编号", "VIP", "vip");
    protected static final Set<String> AFTER_TEXT_BLACK_LIST = Sets.newHashSet("ML", "L", "ml", "l", "年");

    protected static final Set<String> AFTER_TEXT_START_WITH_BLACK_LIST = Sets.newHashSet();

    static {
        try {
            File file = ResourceUtils.getFile("classpath:ocr/price_regex.txt");
            String[] regexList = FileUtils.readFileToString(file, StandardCharsets.UTF_8).split("\n");
            for (String regex : regexList) {
                REGEX_LIST.add(Pattern.compile(regex));
            }
            load(BLACK_LIST, "classpath:ocr/price_black_list.txt");
            load(AFTER_TEXT_START_WITH_BLACK_LIST, "classpath:ocr/price_after_start_with_black_list.txt");
        } catch (IOException ex) {
            throw new MetaDataBusinessException("load resource error.");
        }
    }

    static void load(Set<String> set, String path) throws IOException {
        File file = ResourceUtils.getFile(path);
        set.addAll(Arrays.asList(FileUtils.readFileToString(file, StandardCharsets.UTF_8).split("\n")));
    }


    @Override
    public boolean mark(TextBlock block) {
        if (BLACK_LIST.contains(block.getWords())) {
            return false;
        }
        boolean flag = false;
        for (Pattern regex : REGEX_LIST) {
            Matcher matcher = regex.matcher(block.getWords());
            while (matcher.find()) {
                if (
                        preTextVeryCertain(matcher.group(1)) ||
                                afterTextVeryCertain(matcher.group(3)) ||
                                (preTextGuess(matcher.group(1)) && priceTextGuess(matcher.group(2)) && afterTextGuess(matcher.group(3)))
                ) {
                    block.setType(TextType.PRICE);
                    flag = true;
                }
            }
        }
        return flag;
    }

    private boolean afterTextVeryCertain(String after) {
        for (String word : AFTER_TEXT_START_WITH_WHITE_LIST) {
            if (after.startsWith(word)) {
                return true;
            }
        }
        return false;
    }

    private boolean preTextVeryCertain(String pre) {
        for (String word : PRE_TEXT_END_WITH_WHITE_LIST) {
            if (pre.startsWith(word)) {
                return true;
            }
        }
        return false;
    }

    private boolean preTextGuess(String pre) {
        if (Strings.isNullOrEmpty(pre)) {
            return true;
        }
        for (String word : PRE_TEXT_BLACK_LIST) {
            if (pre.contains(word)) {
                return false;
            }
        }
        return true;
    }

    private boolean priceTextGuess(String price) {
        price = price.replace(",", "");
        price = price.replace("，", "");

        BigDecimal value;
        try {
            value = new BigDecimal(price);
        } catch (NumberFormatException ex) {
            throw new MetaDataBusinessException(String.format("Mark price cause number format exception, value : %s", price));
        }
        return value.compareTo(MIN_PRICE) > 0 && value.compareTo(MAX_PRICE) < 0;
    }

    private boolean afterTextGuess(String after) {
        if (Strings.isNullOrEmpty(after)) {
            return true;
        }
        for (String word : AFTER_TEXT_BLACK_LIST) {
            if (after.contains(word)) {
                return false;
            }
        }
        for (String word : AFTER_TEXT_START_WITH_BLACK_LIST) {
            if (after.startsWith(word)) {
                return false;
            }
        }
        return true;
    }
}
