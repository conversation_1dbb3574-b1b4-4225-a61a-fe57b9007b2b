package com.facishare.crm.fmcg.tpm.dao.mongo.po;


import com.google.common.base.Strings;

import java.util.Objects;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2022/9/30 16:34
 */
public class BudgetTypeExt {

    private final BudgetTypePO budgetType;

    private BudgetTypeExt(BudgetTypePO budgetType) {
        this.budgetType = budgetType;
    }

    public static BudgetTypeExt of(BudgetTypePO po) {
        return new BudgetTypeExt(po);
    }

    public boolean isNull() {
        return Objects.isNull(this.budgetType);
    }

    public BudgetTypeNodeEntity rootNode() {
        return this.budgetType.getNodes().stream().filter(n -> Strings.isNullOrEmpty(n.getParentNodeId())).findFirst().orElse(null);
    }

    public BudgetTypeNodeEntity node(String id) {
        return this.budgetType.getNodes().stream().filter(n -> n.getNodeId().equals(id)).findFirst().orElse(null);
    }

    public BudgetTypeNodeEntity childNode(String parentId) {
        return this.budgetType.getNodes().stream().filter(n -> n.getParentNodeId().equals(parentId)).findFirst().orElse(null);
    }
}