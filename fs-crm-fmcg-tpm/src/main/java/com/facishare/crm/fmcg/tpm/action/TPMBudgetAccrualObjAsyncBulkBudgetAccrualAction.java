package com.facishare.crm.fmcg.tpm.action;

import com.google.common.collect.Lists;;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.predef.action.AbstractStandardAsyncBulkAction;
import lombok.Data;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/9/6 下午3:16
 */
public class TPMBudgetAccrualObjAsyncBulkBudgetAccrualAction extends AbstractStandardAsyncBulkAction<TPMBudgetAccrualObjAsyncBulkBudgetAccrualAction.Arg, TPMBudgetAccrualObjBudgetAccrualAction.Arg> {


    @Override
    protected String getDataIdByParam(TPMBudgetAccrualObjBudgetAccrualAction.Arg arg) {
        return arg.getDataId();
    }

    @Override
    protected List<TPMBudgetAccrualObjBudgetAccrualAction.Arg> getButtonParams() {
        return arg.getDataIds().stream()
                .map(id -> {
                    TPMBudgetAccrualObjBudgetAccrualAction.Arg arg = new TPMBudgetAccrualObjBudgetAccrualAction.Arg();
                    arg.setDataId(id);
                    return arg;
                })
                .collect(Collectors.toList());
    }

    @Override
    protected String getButtonApiName() {
        return ObjectAction.BUDGET_ACCRUAL.getButtonApiName();
    }

    @Override
    protected String getActionCode() {
        return ObjectAction.BUDGET_ACCRUAL.getActionCode();

    }

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Lists.newArrayList(ObjectAction.BUDGET_ACCRUAL.getActionCode());
    }

    @Data
    public static class Arg {
        private List<String> dataIds;
    }
}
