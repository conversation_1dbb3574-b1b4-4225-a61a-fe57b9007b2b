package com.facishare.crm.fmcg.tpm.dao.mongo.po;

import com.facishare.crm.fmcg.tpm.web.contract.model.ActivityCostAssignAcceptConfigVO;
import lombok.Data;
import lombok.ToString;
import org.mongodb.morphia.annotations.Property;

import java.io.Serializable;

/**
 * description : just code
 * <p>
 * create by @wuyx
 * create time 2022/5/30 19:42
 */
@Data
@ToString
public class ActivityCostAssignAcceptConfigEntity implements Serializable {

    /**
     * 是否需要门店签收确认
     */
    @Property("accept_status")
    private Boolean acceptStatus;

    public static ActivityCostAssignAcceptConfigEntity fromVO(ActivityCostAssignAcceptConfigVO vo) {
        if (vo == null) {
            return null;
        }
        ActivityCostAssignAcceptConfigEntity po = new ActivityCostAssignAcceptConfigEntity();
        po.setAcceptStatus(vo.getAcceptStatus() == null ? false : vo.getAcceptStatus());
        return po;
    }
}
