package com.facishare.crm.fmcg.tpm.retry.annotation;

import com.facishare.crm.fmcg.tpm.retry.handler.RetryHandlerEnum;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
public @interface LocalRetryHandlerType {
    RetryHandlerEnum name() ;
}
