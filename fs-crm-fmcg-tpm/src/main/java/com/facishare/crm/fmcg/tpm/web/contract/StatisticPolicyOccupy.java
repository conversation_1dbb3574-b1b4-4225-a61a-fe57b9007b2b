package com.facishare.crm.fmcg.tpm.web.contract;

import lombok.Builder;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * author: wuyx
 * description:
 * createTime: 2023/9/12 20:35
 */
@Data
@ToString
public class StatisticPolicyOccupy implements Serializable {

    @Data
    @ToString
    public static class Arg implements Serializable {

        private String tenantId;

        private String userId;

        private List<DetailLimitObj> dataList;
    }

    @Data
    @ToString
    @Builder
    public static class Result implements Serializable {

        private String code;

        private String msg;
    }

    @Data
    @ToString
    public static class DetailLimitObj implements Serializable {

        private String accountId;

        private String activityId;

        private BigDecimal occupy;

        private String limitObjType;

        private String detailLimitId;

        private String accountMode;

        private String range;
    }
}