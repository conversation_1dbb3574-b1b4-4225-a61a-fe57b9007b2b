package com.facishare.crm.fmcg.tpm.api.rule;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.crm.fmcg.tpm.api.SimpleDTO;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * Author: linmj
 * Date: 2023/9/15 15:37
 */
@Data
@ToString
public class RewardFieldDescribeDTO {

    @JsonProperty(value = "field_name")
    @SerializedName("field_name")
    @JSONField(name = "field_name")
    private String fieldName;

    @JsonProperty(value = "field_describe")
    @SerializedName("field_describe")
    @JSONField(name = "field_describe")
    private String fieldDescribe;


    @JsonProperty(value = "field_type")
    @SerializedName("field_type")
    @JSONField(name = "field_type")
    private String fieldType;

    @JsonProperty(value = "options")
    @SerializedName("options")
    @JSONField(name = "options")
    private List<SimpleDTO> options;
}
