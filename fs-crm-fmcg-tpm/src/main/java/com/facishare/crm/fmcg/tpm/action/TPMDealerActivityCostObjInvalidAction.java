package com.facishare.crm.fmcg.tpm.action;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.fmcg.common.apiname.*;
import com.facishare.crm.fmcg.common.gray.TPMGrayUtils;
import com.facishare.crm.fmcg.tpm.api.enumeration.LogType;
import com.facishare.crm.fmcg.tpm.api.log.LogData;
import com.facishare.crm.fmcg.tpm.api.method.IdempotentArgBase;
import com.facishare.crm.fmcg.tpm.business.BudgetService;
import com.facishare.crm.fmcg.tpm.business.OperateInfoService;
import com.facishare.crm.fmcg.tpm.business.abstraction.IDealerActivityCostRebateService;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityTypeExt;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityWriteOffSourceConfigEntity;
import com.facishare.crm.fmcg.tpm.service.TransactionProxy;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.crm.fmcg.common.utils.QueryDataUtil;
import com.facishare.crm.fmcg.tpm.web.manager.ActivityTypeManager;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.StandardInvalidAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.github.trace.TraceContext;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2020/12/16 下午4:02
 */
//IgnoreI18nFile
@SuppressWarnings("Duplicates,unused")
public class TPMDealerActivityCostObjInvalidAction extends StandardInvalidAction {

    private static final BudgetService budgetService = SpringUtil.getContext().getBean(BudgetService.class);
    private static final OperateInfoService operateInfoService = SpringUtil.getContext().getBean(OperateInfoService.class);
    private static final ActivityTypeManager activityTypeManager = SpringUtil.getContext().getBean(ActivityTypeManager.class);
    private static final TransactionProxy trans = SpringUtil.getContext().getBean(TransactionProxy.class);
    private static final IDealerActivityCostRebateService dealerActivityCostRebateService = SpringUtil.getContext().getBean(IDealerActivityCostRebateService.class);

    @Override
    protected void before(Arg arg) {
        super.before(arg);
        this.stopWatch.lap("super.before");

        // 校验当前核销数据是否允许被作废
        this.validateAllowInvalid();
        this.stopWatch.lap("validateAllowInvalid");
    }

    @Override
    protected Result doAct(Arg arg) {
        return trans.call(() -> {
            Result result = super.doAct(arg);
            this.stopWatch.lap("super.doAct");

            IObjectData cost = result.getObjectData().toObjectData();
            String activityId = cost.get(TPMDealerActivityCostFields.ACTIVITY_ID, String.class);

            // 处理预算
            this.handleBudget(activityId, arg);
            this.stopWatch.lap("handleBudget");

            // 移除核销依据以及相关对象对当前核销对象的引用
            this.removeReference(activityId, cost.getId());
            this.stopWatch.lap("removeReference");

            return result;
        });
    }

    @Override
    protected Result after(Arg arg, Result result) {
        dealerActivityCostRebateService.invalidRebateObjData(actionContext.getRequestContext(), Lists.newArrayList(result.getObjectData().getId()));
        return super.after(arg, result);
    }

    private void validateAllowInvalid() {
        IObjectData cost = serviceFacade.findObjectData(User.systemUser(actionContext.getTenantId()), arg.getObjectDataId(), ApiNames.TPM_DEALER_ACTIVITY_COST);
        boolean enterIntoAccount = cost.get("enter_into_account", Boolean.class, false);

        // 对接了客户账户，已经入账的核销数据不允许作废
        if (enterIntoAccount) {
            throw new ValidateException(I18N.text(I18NKeys.THIS_SINGLE_COST_HAS_TRANSFER_IN_ACCOUNT_DO_NOT_SUPPORT_INVALID));
        }
    }

    private void removeReference(String activityId, String costId) {
        ActivityTypeExt type = activityTypeManager.findByActivityId(actionContext.getTenantId(), activityId);
        ActivityWriteOffSourceConfigEntity config = type.writeOffSourceConfig();

        if (Objects.isNull(config) || Strings.isNullOrEmpty(config.getApiName())) {
            return;
        }

        // 移除核销依据的查找关联关系
        this.removeWriteOffSourceReference(costId, config);

        // 如果核销依据是举证检核对象，举证数据的关联关系也需要移除掉
        if (config.getApiName().equals(ApiNames.TPM_ACTIVITY_PROOF_AUDIT_OBJ)) {
            this.removeProofReference(costId);
        }
    }

    private void removeWriteOffSourceReference(String costId, ActivityWriteOffSourceConfigEntity config) {
        this.removeObjectReference(costId, config.getApiName(), config.getReferenceWriteOffFieldApiName());
    }

    private void removeProofReference(String costId) {
        this.removeObjectReference(costId, ApiNames.TPM_ACTIVITY_PROOF_OBJ, TPMActivityProofFields.DEALER_ACTIVITY_COST_ID);
    }

    private void removeObjectReference(String costId, String objectApiName, String referenceFieldApiName) {
        IFilter costFilter = new Filter();
        costFilter.setFieldName(referenceFieldApiName);
        costFilter.setOperator(Operator.EQ);
        costFilter.setFieldValues(Lists.newArrayList(costId));

        SearchTemplateQuery stq = QueryDataUtil.minimumQuery(costFilter);

        List<IObjectData> allData = QueryDataUtil.find(serviceFacade, actionContext.getTenantId(), objectApiName, stq, Lists.newArrayList(CommonFields.ID, CommonFields.TENANT_ID, CommonFields.OBJECT_DESCRIBE_API_NAME));

        List<String> fields = Lists.newArrayList(referenceFieldApiName);
        for (List<IObjectData> data : Lists.partition(allData, 200)) {
            data.forEach(datum -> datum.set(referenceFieldApiName, ""));
            serviceFacade.batchUpdateByFields(User.systemUser(actionContext.getTenantId()), data, fields);
        }
    }

    private void handleBudget(String activityId, Arg arg) {
        IObjectData activity = serviceFacade.findObjectData(User.systemUser(actionContext.getTenantId()), activityId, ApiNames.TPM_ACTIVITY_OBJ);

        if (!budgetService.isOpenBudge(Integer.parseInt(actionContext.getTenantId()))) {
            budgetService.calculateActivity(actionContext.getTenantId(), activityId);
            return;
        }

        try {
            String budgetId = activity.get(TPMActivityFields.BUDGET_TABLE, String.class, "");

            if (Strings.isNullOrEmpty(activityId)) {
                return;
            }

            if (!Strings.isNullOrEmpty(budgetId)) {
                budgetService.tryLockBudget(actionContext, budgetId);
            } else {
                budgetService.tryLockBudget(actionContext, activityId);
            }

            if (TPMGrayUtils.excessDeductionForCost(actionContext.getTenantId())) {
                Map<String, Double> amountMap = budgetService.calculateActivity(actionContext.getTenantId(), activity.getId());
                double activityAmount = Double.parseDouble(activity.get(TPMActivityFields.ACTIVITY_AMOUNT, String.class, "0"));
                double actualAmount = Double.parseDouble(activity.get(TPMActivityFields.ACTIVITY_ACTUAL_AMOUNT, String.class, "0"));
                double calActivityAmount = amountMap.getOrDefault(TPMActivityFields.ACTIVITY_ACTUAL_AMOUNT, 0D);
                if (activityAmount < actualAmount) {
                    double change = actualAmount - Math.max(calActivityAmount, activityAmount);
                    LogData logData = LogData.builder().data(JSON.toJSONString(arg)).build();
                    String logId = operateInfoService.log(actionContext.getTenantId(), LogType.INVALID.value(), JSON.toJSONString(logData), actionContext.getUser().getUpstreamOwnerIdOrUserId(), ApiNames.TPM_DEALER_ACTIVITY_COST, arg.getObjectDataId(), false);
                    IObjectData lastDetail = budgetService.getLastBudgetDetail(actionContext.getTenantId(), budgetId);

                    double beforeAmount = lastDetail.get(TPMActivityBudgetDetailFields.AMOUNT_AFTER_OPERATION, Double.class, 0D);
                    double afterAmount = beforeAmount + change;

                    budgetService.addBudgetDetail(actionContext.getTenantId(), actionContext.getUser().getUpstreamOwnerIdOrUserId(), "2", budgetId, String.format("「%s」活动方案超额核销作废", activity.getName()), change, beforeAmount, afterAmount, System.currentTimeMillis(), String.format("LogId:%s-traceId:%s", logId, TraceContext.get().getTraceId()), activity.getId(), TraceContext.get().getTraceId(), IdempotentArgBase.builder().idempotentKey(actionContext.getPostId() + ":" + budgetId).build());
                }
                budgetService.calculateBudget(actionContext.getTenantId(), budgetId);
            } else {
                budgetService.calculateActivity(actionContext.getTenantId(), activityId);
                budgetService.calculateBudget(actionContext.getTenantId(), budgetId);
            }
        } finally {
            budgetService.unLockBudget(actionContext);
        }
    }
}
