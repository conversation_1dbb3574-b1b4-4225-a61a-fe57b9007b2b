package com.facishare.crm.fmcg.tpm.controller;

import com.alibaba.fastjson.JSONObject;
import com.facishare.paas.appframework.core.predef.controller.StandardListHeaderController;

import java.util.List;
import java.util.stream.Collectors;


public class StorePromotionRecordObjListHeaderController extends StandardListHeaderController {


    @Override
    protected Result after(Arg arg, Result result) {
        List<JSONObject> buttons = (List<JSONObject>) result.getLayout().get("buttons");
        result.getLayout().put("buttons", buttons.stream().filter(v -> !v.getString("action").equals("Add") && !v.getString("action").equals("Import") && !v.getString("action").equals("Edit")).collect(Collectors.toList()));
        return super.after(arg, result);
    }
}
