package com.facishare.crm.fmcg.tpm.business.abstraction;

import com.facishare.paas.metadata.api.IObjectData;

/**
 * Author: linmj
 * Date: 2024/3/4 15:50
 */
public interface IMengNiuAICalculateService {

    boolean isFitMengNiuAiRule(String activityTenantId, String activityId, String dataTenantId, String ruleString, String triggerApiName, String triggerDataId, boolean isWriteError);

    boolean isFitMengNiuAiRule(IObjectData activity, String dataTenantId, String triggerApiName, String triggerDataId, boolean isWriteError);

    boolean isFitMengNiuProductRule(IObjectData activity, String dataTenantId, String triggerApiName, String triggerDataId, boolean isWriteError);

    void validateAIRewardRule(String ruleString);
    void validateOrderProductRule(String ruleString);
}
