package com.facishare.crm.fmcg.tpm.web.contract;

import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/3/20 16:07
 */
public interface UpdateRedPacketExpire {

    @Data
    @ToString
    class Arg implements Serializable {

        private String tenantId;

        private String activityId;

        private Long expireTime;

        private String status;

        private List<String> dataIds;

        /**
         * 是否根据过期时间进行过滤
         */
        private Boolean filterByExpirationTime;
    }
}
