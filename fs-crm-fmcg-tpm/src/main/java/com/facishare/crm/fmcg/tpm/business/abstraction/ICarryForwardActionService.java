package com.facishare.crm.fmcg.tpm.business.abstraction;

import com.facishare.paas.appframework.core.model.User;

/**
 * 异步结转核心逻辑
 * <p>
 * create by @yangqf
 * create time 2022/9/15 14:20
 */
public interface ICarryForwardActionService {

    void carryForward(User user, String dataId);

    void freeze(User user, String dataId);

    void unfreeze(User user, String dataId);

    void initRetryButton(String tenantId);
}