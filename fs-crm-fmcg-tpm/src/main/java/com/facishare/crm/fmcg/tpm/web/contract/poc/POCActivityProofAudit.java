package com.facishare.crm.fmcg.tpm.web.contract.poc;

import com.facishare.paas.metadata.api.IObjectData;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

public interface POCActivityProofAudit {
    @Data
    @EqualsAndHashCode(callSuper = true)
    @ToString
    class Arg extends POCBase.Arg implements Serializable {
        private IObjectData proof;
        private List<IObjectData> proofDetails;


    }

    @Data
    @EqualsAndHashCode(callSuper = true)
    @ToString
    class Result extends POCBase.Result implements Serializable {



    }
}
