package com.facishare.crm.fmcg.tpm.button.provider;

import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.tpm.button.abs.AbstractTPMSpecialButtonProvider;
import com.facishare.crm.fmcg.tpm.utils.ButtonUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.metadata.ui.layout.IButton;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/7/5 上午10:49
 */
@Component
public class TPMStoreWriteOffObjSpecialButtonProvider extends AbstractTPMSpecialButtonProvider {

    private static final Logger LOG = LoggerFactory.getLogger(TPMStoreWriteOffObjSpecialButtonProvider.class);

    @Override
    public String getApiName() {
        return ApiNames.TPM_STORE_WRITE_OFF_OBJ;
    }

    @Override
    public List<IButton> getSpecialButtons() {

        List<IButton> buttons = super.getSpecialButtons();
        buttons.add(ButtonUtils.buildButton(ObjectAction.COST_WRITE_OFF));
        return buttons;
    }
}
