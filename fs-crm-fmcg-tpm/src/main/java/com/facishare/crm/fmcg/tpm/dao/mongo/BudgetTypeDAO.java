package com.facishare.crm.fmcg.tpm.dao.mongo;

import com.facishare.crm.fmcg.tpm.dao.mongo.po.BudgetDimensionEntity;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.paas.I18N;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.BudgetTypeNodeEntity;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.BudgetTypePO;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.MongoPO;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.google.common.base.Strings;
import org.apache.commons.collections4.CollectionUtils;
import org.mongodb.morphia.query.Query;
import org.mongodb.morphia.query.UpdateOperations;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2021/11/15 16:36
 */
public class BudgetTypeDAO extends UniqueIdBaseDAO<BudgetTypePO> {

    protected BudgetTypeDAO(Class<BudgetTypePO> clazz) {
        super(clazz);
    }

    public void setStatus(String tenantId, Integer operator, String uniqueId, String status) {
        Query<BudgetTypePO> query = mongoContext.createQuery(BudgetTypePO.class)
                .field(MongoPO.F_TENANT_ID).equal(tenantId)
                .field(MongoPO.F_UNIQUE_ID).equal(uniqueId);

        UpdateOperations<BudgetTypePO> updateOperations = mongoContext.createUpdateOperations(BudgetTypePO.class)
                .set(BudgetTypePO.F_STATUS, status)
                .inc(BudgetTypePO.F_VERSION)
                .set(MongoPO.F_LAST_UPDATER, operator)
                .set(MongoPO.F_LAST_UPDATE_TIME, System.currentTimeMillis());

        mongoContext.update(query, updateOperations);
    }

    public List<BudgetTypePO> list(String tenantId, String keyword) {
        Query<BudgetTypePO> query = mongoContext.createQuery(BudgetTypePO.class)
                .field(MongoPO.F_TENANT_ID).equal(tenantId)
                .field(MongoPO.F_IS_DELETED).equal(false)
                .order("-" + MongoPO.F_LAST_UPDATE_TIME);
        if (!Strings.isNullOrEmpty(keyword)) {
            query.field(BudgetTypePO.F_NAME).containsIgnoreCase(keyword);
        }
        return query.asList();
    }

    public long count(String tenantId, String keyword) {
        Query<BudgetTypePO> query = mongoContext.createQuery(BudgetTypePO.class)
                .field(MongoPO.F_TENANT_ID).equal(tenantId)
                .field(MongoPO.F_IS_DELETED).equal(false);

        if (!Strings.isNullOrEmpty(keyword)) {
            query.field(BudgetTypePO.F_NAME).containsIgnoreCase(keyword);
        }
        return query.countAll();
    }

    public boolean templateIsUsed(String tenantId, String id) {
        Query<BudgetTypePO> query = mongoContext.createQuery(BudgetTypePO.class)
                .field(MongoPO.F_TENANT_ID).equal(tenantId)
                .field(MongoPO.F_IS_DELETED).equal(false)
                .field("nodes.template_id").equal(id);
        return query.countAll() > 0;
    }

    public boolean isDuplicateName(String tenantId, String name) {
        Query<BudgetTypePO> query = mongoContext.createQuery(BudgetTypePO.class)
                .field(MongoPO.F_TENANT_ID).equal(tenantId)
                .field(MongoPO.F_IS_DELETED).equal(false)
                .field(BudgetTypePO.F_NAME).equal(name);
        return query.countAll() > 0;
    }

    public boolean isDuplicateName(String tenantId, String uniqueId, String name) {
        Query<BudgetTypePO> query = mongoContext.createQuery(BudgetTypePO.class)
                .field(MongoPO.F_TENANT_ID).equal(tenantId)
                .field(MongoPO.F_IS_DELETED).equal(false)
                .field(MongoPO.F_UNIQUE_ID).notEqual(uniqueId)
                .field(BudgetTypePO.F_NAME).equal(name);
        return query.countAll() > 0;
    }

    public BudgetTypePO getByDepartment(String tenantId, List<Integer> departmentIds) {
        Query<BudgetTypePO> query = mongoContext.createQuery(BudgetTypePO.class)
                .field(MongoPO.F_TENANT_ID).equal(tenantId)
                .field(MongoPO.F_IS_DELETED).equal(false)
                .field(BudgetTypePO.F_DEPARTMENT_RANGE).hasAnyOf(departmentIds);
        if (query.countAll() > 1) {
            throw new ValidateException(I18N.text(I18NKeys.BUDGET_TYPE_D_A_O_0));
        }
        return query.get();
    }

    public List<BudgetTypeNodeEntity> querySameControlDimensionNode(String tenantId, String budgetTypeId, String controlStrategy, String controlTimeDimension, List<BudgetDimensionEntity> controlDimensions) {
        List<BudgetTypeNodeEntity> nodes = new ArrayList<>();
        BudgetTypePO budgetTypePO = get(tenantId, budgetTypeId);
        if (budgetTypePO != null) {
            if (!CollectionUtils.isEmpty(budgetTypePO.getNodes())) {
                budgetTypePO.getNodes().forEach(node -> {
                    if (controlStrategy.equalsIgnoreCase(node.getControlStrategy()) &&
                            controlTimeDimension.equalsIgnoreCase(node.getControlTimeDimension())) {
                        List<BudgetDimensionEntity> dimensions1 = CollectionUtils.isEmpty(controlDimensions) ? new ArrayList<>() : controlDimensions;
                        List<BudgetDimensionEntity> dimensions2 = CollectionUtils.isEmpty(node.getControlDimensions()) ? new ArrayList<>() : node.getControlDimensions();
                        if (dimensions1.size() == dimensions2.size()) {
                            dimensions1.sort(Comparator.comparing(BudgetDimensionEntity::getApiName));
                            dimensions2.sort(Comparator.comparing(BudgetDimensionEntity::getApiName));
                            boolean isFit = true;
                            for (int i = 0; i < dimensions1.size(); i++) {
                                BudgetDimensionEntity before = dimensions1.get(i);
                                BudgetDimensionEntity after = dimensions2.get(i);
                                if (!before.getApiName().equalsIgnoreCase(after.getApiName()) || !Strings.isNullOrEmpty(before.getType()) && !before.getType().equalsIgnoreCase(after.getType())) {
                                    isFit = false;
                                    break;
                                }
                            }
                            if (isFit) {
                                nodes.add(node);
                            }
                        }
                    }
                });
            }
        }
        return nodes;
    }

    public List<BudgetTypeNodeEntity> querySameControlDimensionNode(String tenantId, String budgetTypeId, String nodeId) {
        List<BudgetTypeNodeEntity> nodes = new ArrayList<>();
        BudgetTypePO budgetTypePO = get(tenantId, budgetTypeId);
        if (budgetTypePO != null) {
            if (!CollectionUtils.isEmpty(budgetTypePO.getNodes())) {
                BudgetTypeNodeEntity baseNode = budgetTypePO.getNodes().stream().filter(v -> v.getNodeId().equals(nodeId)).findFirst().orElse(null);
                if (baseNode == null) {
                    return nodes;
                }
                budgetTypePO.getNodes().forEach(node -> {
                    if (baseNode.getControlStrategy().equalsIgnoreCase(node.getControlStrategy()) &&
                            baseNode.getControlTimeDimension().equalsIgnoreCase(node.getControlTimeDimension())) {
                        List<BudgetDimensionEntity> dimensions1 = CollectionUtils.isEmpty(baseNode.getControlDimensions()) ? new ArrayList<>() : baseNode.getControlDimensions();
                        List<BudgetDimensionEntity> dimensions2 = CollectionUtils.isEmpty(node.getControlDimensions()) ? new ArrayList<>() : node.getControlDimensions();
                        if (dimensions1.size() == dimensions2.size()) {
                            dimensions1.sort(Comparator.comparing(BudgetDimensionEntity::getApiName));
                            dimensions2.sort(Comparator.comparing(BudgetDimensionEntity::getApiName));
                            boolean isFit = true;
                            for (int i = 0; i < dimensions1.size(); i++) {
                                BudgetDimensionEntity before = dimensions1.get(i);
                                BudgetDimensionEntity after = dimensions2.get(i);
                                if (!before.getApiName().equalsIgnoreCase(after.getApiName()) || !Strings.isNullOrEmpty(before.getType()) && !before.getType().equalsIgnoreCase(after.getType())) {
                                    isFit = false;
                                    break;
                                }
                            }
                            if (isFit) {
                                nodes.add(node);
                            }
                        }
                    }
                });
            }
        }
        return nodes;
    }

    public BudgetTypeNodeEntity getNodeById(String tenantId, String nodeId) {
        Query<BudgetTypePO> query = mongoContext.createQuery(BudgetTypePO.class)
                .field(MongoPO.F_TENANT_ID).equal(tenantId)
                .field(MongoPO.F_IS_DELETED).equal(false);
        query.field(BudgetTypePO.F_NODES + "." + BudgetTypeNodeEntity.F_NODE_ID).equal(nodeId);
        BudgetTypePO po = query.get();
        return po == null ? null : po.getNodes().stream().filter(v -> v.getNodeId().equals(nodeId)).findFirst().orElse(null);
    }

    public Boolean isDuplicateApiName(String tenantId, String apiName) {
        Query<BudgetTypePO> query = mongoContext.createQuery(BudgetTypePO.class)
                .field(MongoPO.F_TENANT_ID).equal(tenantId)
                .field(BudgetTypePO.F_NAME).equal(apiName);
        return query.countAll() > 0;
    }

    public Boolean isDuplicateNodeApiName(String tenantId, String apiName) {
        Query<BudgetTypePO> query = mongoContext.createQuery(BudgetTypePO.class)
                .field(MongoPO.F_TENANT_ID).equal(tenantId)
                .field("nodes.api_name").equal(apiName);
        return query.countAll() > 0;
    }
}
