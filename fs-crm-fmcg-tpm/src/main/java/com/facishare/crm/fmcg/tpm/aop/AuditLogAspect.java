package com.facishare.crm.fmcg.tpm.aop;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.fmcg.tpm.business.CrmAuditLogService;
import com.facishare.crm.fmcg.tpm.business.dto.CrmAuditLogDTO;
import com.facishare.crm.fmcg.tpm.web.annotation.SendLog;
import com.facishare.crm.fmcg.common.http.ApiContext;
import com.facishare.crm.fmcg.common.http.ApiContextManager;
import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.lang.reflect.Method;

/**
 * <AUTHOR>
 * @date 2022/12/16 下午5:35
 */
@Aspect
@Component
@Slf4j
public class AuditLogAspect {

    @Resource
    private CrmAuditLogService crmAuditLogService;

    @Pointcut("@annotation(com.facishare.crm.fmcg.tpm.web.annotation.SendLog)")
    public void sendLog() {
    }

    @Before(value = "sendLog() && args(arg) ")
    public void sendLogArg(JoinPoint joinPoint, Object arg) {
        ApiContext context = ApiContextManager.getContext();
        LogParameter logParam = getLogParam(joinPoint);

        crmAuditLogService.sendLog(CrmAuditLogDTO.builder()
                .tenantId(context.getTenantId())
                .userId(String.valueOf(context.getEmployeeId()))
                .action(joinPoint.getSignature().getName())
                .message(logParam.getMessage())
                .objectApiNames(logParam.getObjectApiName())
                .parameters(JSON.toJSONString(arg))
                .build());
    }

    private LogParameter getLogParam(JoinPoint joinPoint) {

        MethodSignature methodSignature = (MethodSignature) joinPoint.getSignature();
        if (methodSignature == null) {
            return LogParameter.builder().build();
        }
        Method method = methodSignature.getMethod();
        String message = method.getAnnotation(SendLog.class).message();
        String objectApiName = method.getAnnotation(SendLog.class).objectApiName();
        return LogParameter.builder().message(message).objectApiName(objectApiName).build();
    }

    @Data
    @Builder
    static class LogParameter {

        String message;

        String objectApiName;
    }
}
