package com.facishare.crm.fmcg.tpm.api.scan;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.crm.fmcg.tpm.reward.dto.WeChatArg;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;

public interface ConsumerScanUnlock {

    @Data
    @ToString
    @EqualsAndHashCode(callSuper = true)
    class Arg extends WeChatArg implements Serializable {

        @JSONField(name = "qr_code_id")
        @JsonProperty(value = "qr_code_id")
        @SerializedName("qr_code_id")
        private String qrCodeId;
    }

    @Data
    @ToString
    @Builder
    class Result implements Serializable {

        @JSONField(name = "product_name")
        @JsonProperty(value = "product_name")
        @SerializedName("product_name")
        private String productName;

        @JSONField(name = "product_code")
        @JsonProperty(value = "product_code")
        @SerializedName("product_code")
        private String productCode;
    }
}