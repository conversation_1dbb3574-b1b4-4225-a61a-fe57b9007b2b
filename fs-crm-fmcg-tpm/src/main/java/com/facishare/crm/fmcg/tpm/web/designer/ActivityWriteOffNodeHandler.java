package com.facishare.crm.fmcg.tpm.web.designer;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.paas.I18N;
import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.common.apiname.TPMActivityProofAuditFields;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.NodeType;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.WriteOffCalculateType;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.WriteOffChargeUpTypeEnum;
import com.facishare.crm.fmcg.tpm.web.contract.model.ActivityNodeVO;
import com.facishare.crm.fmcg.tpm.web.contract.model.ActivityWriteOffChargeUpConfigVO;
import com.facishare.crm.fmcg.tpm.web.contract.model.ActivityWriteOffSourceConfigVO;
import com.facishare.crm.fmcg.tpm.web.contract.model.IActivityType;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.metadata.DescribeLogicService;
import com.facishare.paas.appframework.metadata.FieldDescribeExt;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.fmcg.framework.http.FmcgServiceProxy;
import com.fmcg.framework.http.PaasDataProxy;
import com.fmcg.framework.http.contract.fmcgservice.GetTenantConfig;
import com.fmcg.framework.http.contract.fmcgservice.SetTenantConfig;
import com.fmcg.framework.http.contract.paas.data.PaasEnableCustomerAccount;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2021/11/30 12:14
 */
//IgnoreI18nFile
@Slf4j
@Component("activityWriteOffNodeHandler")
public class ActivityWriteOffNodeHandler extends BaseSystemNodeHandler {

    @Resource(name = "describeLogicService")
    private DescribeLogicService describeService;

    @Resource
    private PaasDataProxy paasDataProxy;

    @Resource
    private FmcgServiceProxy fmcgServiceProxy;

    @Override
    public String getNodeType() {
        return NodeType.WRITE_OFF.value();
    }

    @Override
    public void validation(String tenantId, int index, List<ActivityNodeVO> nodes, IActivityType activityType) {
        super.validation(tenantId, index, nodes, activityType);
        ActivityNodeVO node = nodes.get(index);

        if (Objects.isNull(node.getActivityWriteOffConfig())) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_WRITE_OFF_NODE_HANDLER_0));
        }

        boolean checkForbidCustomerRelated = Boolean.TRUE.equals(activityType.getForbidRelateCustomer());

        writeOffSourceConfigValidation(tenantId, node.getActivityWriteOffConfig().getWriteOffSourceConfig(), nodes);

        //暂时去掉对上账信息的校验
        chargeUpConfigValidation(checkForbidCustomerRelated, node.getActivityWriteOffConfig().getChargeUpConfig());
        //开启上账账户
        if (Objects.nonNull(node.getActivityWriteOffConfig().getChargeUpConfig())
                && Boolean.TRUE.equals(node.getActivityWriteOffConfig().getChargeUpConfig().getChargeUpAccountStatus())) {
            openChargeAccount(Integer.parseInt(tenantId));
        }
        if (checkForbidCustomerRelated) {
            if (!WriteOffCalculateType.ACTIVITY.value().equals(node.getActivityWriteOffConfig().getWriteOffSourceConfig().getCalculateType())) {
                throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_WRITE_OFF_NODE_HANDLER_1));
            }
        }

    }


    private void openChargeAccount(int tenantId) {
        String chargeAccountKey = "CHARGE_ACCOUNT";
        GetTenantConfig.Arg getConfigArg = new GetTenantConfig.Arg();
        getConfigArg.setKey(chargeAccountKey);
        GetTenantConfig.Result getConfigResult = fmcgServiceProxy.getTenantConfig(tenantId, -10000, getConfigArg);

        if (Objects.isNull(getConfigResult)) {
            throw new ValidateException(String.format("'%s' config is null", chargeAccountKey));
        }
        log.info("tenantId : {}, '{}' config value : {}", tenantId, chargeAccountKey, getConfigResult.getValue());

        if (Boolean.FALSE.toString().equals(getConfigResult.getValue())) {
            PaasEnableCustomerAccount.Result result = paasDataProxy.enableCustomerAccount(tenantId, -10000, new PaasEnableCustomerAccount.Arg());
            log.info("passEnableCustomerAccountResult ={}", JSON.toJSONString(result));
            if (result.getResult() != null && result.getResult().getEnableStatus() != null && result.getResult().getEnableStatus().equals(2)) {

                SetTenantConfig.Arg setConfigArg = new SetTenantConfig.Arg();
                setConfigArg.setKey(chargeAccountKey);
                setConfigArg.setValue(Boolean.TRUE.toString());
                fmcgServiceProxy.setTenantConfig(tenantId, -10000, setConfigArg);
            } else if (Objects.nonNull(result.getErrMessage()) && !"OK".equals(result.getErrMessage())) {
                throw new ValidateException(String.format(I18N.text(I18NKeys.ACTIVITY_WRITE_OFF_NODE_HANDLER_2), result.getErrMessage()));
            }
        }
    }

    private void writeOffSourceConfigValidation(String tenantId, ActivityWriteOffSourceConfigVO config, List<ActivityNodeVO> nodes) {
        if (Objects.isNull(config) || Strings.isNullOrEmpty(config.getTemplateId())) {
            return;
        }

        ActivityNodeVO sourceNode = nodes.stream().filter(f -> f.getTemplateId().equals(config.getTemplateId())).findFirst().orElse(null);
        if (sourceNode == null) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_WRITE_OFF_NODE_HANDLER_3));
        }

        config.setApiName(sourceNode.getObjectApiName());
        config.setRecordType(sourceNode.getObjectRecordType());
        config.setReferenceActivityFieldApiName(sourceNode.getReferenceActivityFieldApiName());

        if (Strings.isNullOrEmpty(config.getApiName())) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_WRITE_OFF_NODE_HANDLER_4));
        }
        if (Strings.isNullOrEmpty(config.getCalculateType())) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_WRITE_OFF_NODE_HANDLER_5));
        }

        if (!ApiNames.TPM_ACTIVITY_PROOF_AUDIT_OBJ.equals(config.getApiName())) {
            IObjectDescribe describe = describeService.findObject(tenantId, config.getApiName());
            if (Objects.isNull(describe)) {
                throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_WRITE_OFF_NODE_HANDLER_6));
            }


            writeOffSourceReferenceFieldValidation(config.getDealerFieldApiName(), ApiNames.ACCOUNT_OBJ, "查找关联经销商", false, describe);
            writeOffSourceReferenceFieldValidation(config.getAccountFieldApiName(), ApiNames.ACCOUNT_OBJ, "查找关联客户", true, describe);
            writeOffSourceReferenceFieldValidation(config.getReferenceWriteOffFieldApiName(), ApiNames.TPM_DEALER_ACTIVITY_COST, "查找关联费用核销", true, describe);
            writeOffSourceCostFieldApiNameValidation(config.getCostFieldApiName(), "应核销费用字段", describe);

            for (String fieldApiName : config.getDisplayFieldApiNames()) {
                writeOffSourceApiNameFieldValidation(fieldApiName, describe);
            }
        } else if (ApiNames.TPM_ACTIVITY_PROOF_AUDIT_OBJ.equals(config.getApiName())) {
            config.setDealerFieldApiName(TPMActivityProofAuditFields.DEALER_ID);
            config.setAccountFieldApiName(TPMActivityProofAuditFields.STORE_ID);
            config.setCostFieldApiName(TPMActivityProofAuditFields.AUDIT_TOTAL);
            config.setReferenceWriteOffFieldApiName(TPMActivityProofAuditFields.DEALER_ACTIVITY_COST_ID);
        }
    }

    private void writeOffSourceCostFieldApiNameValidation(String costFieldApiName, String displayName, IObjectDescribe describe) {
        if (!Strings.isNullOrEmpty(costFieldApiName) && !describe.getFieldDescribeMap().containsKey(costFieldApiName)) {
            throw new ValidateException(String.format(I18N.text(I18NKeys.ACTIVITY_WRITE_OFF_NODE_HANDLER_7), displayName));
        }
    }

    private void writeOffSourceApiNameFieldValidation(String fieldApiName, IObjectDescribe describe) {
        if (!describe.getFieldDescribeMap().containsKey(fieldApiName)) {
            throw new ValidateException(String.format(I18N.text(I18NKeys.ACTIVITY_WRITE_OFF_NODE_HANDLER_8), fieldApiName));
        }
    }

    private void writeOffSourceReferenceFieldValidation(String fieldApiName, String referenceTargetApiName, String displayName, boolean isRequired, IObjectDescribe describe) {
        if (!isRequired && Strings.isNullOrEmpty(fieldApiName)) {
            return;
        }
        if (Strings.isNullOrEmpty(fieldApiName)) {
            throw new ValidateException(String.format(I18N.text(I18NKeys.ACTIVITY_WRITE_OFF_NODE_HANDLER_9), displayName));
        }
        if (!describe.getFieldDescribeMap().containsKey(fieldApiName)) {
            throw new ValidateException(String.format(I18N.text(I18NKeys.ACTIVITY_WRITE_OFF_NODE_HANDLER_10), fieldApiName));
        }
        IFieldDescribe field = describe.getFieldDescribe(fieldApiName);
        if (!"object_reference".equals(field.getType())) {
            throw new ValidateException(String.format(I18N.text(I18NKeys.ACTIVITY_WRITE_OFF_NODE_HANDLER_11), displayName));
        }
        String targetApiName = FieldDescribeExt.of(field).getRefObjTargetApiName();
        if (Strings.isNullOrEmpty(targetApiName) || !referenceTargetApiName.equals(targetApiName)) {
            throw new ValidateException(String.format(I18N.text(I18NKeys.ACTIVITY_WRITE_OFF_NODE_HANDLER_12), displayName));
        }
    }

    private void chargeUpConfigValidation(Boolean forbidRelateCustomer, ActivityWriteOffChargeUpConfigVO chargeUpConfig) {
        if (Objects.nonNull(chargeUpConfig) && Boolean.TRUE.equals(chargeUpConfig.getChargeUpAccountStatus())) {
            if (WriteOffChargeUpTypeEnum.AUTO.code().equals(chargeUpConfig.getChargeUpType())) {
                if (Strings.isNullOrEmpty(chargeUpConfig.getCashAccountId())) {
                    throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_WRITE_OFF_NODE_HANDLER_13));
                }
                if (Strings.isNullOrEmpty(chargeUpConfig.getReturnGoodsQuantityAccountId())) {
                    throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_WRITE_OFF_NODE_HANDLER_14));
                }
            }
            if (forbidRelateCustomer) {
                throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_WRITE_OFF_NODE_HANDLER_15));
            }
        }
    }

    @Override
    protected List<String> queryPostSystemNodeTypes() {
        return Lists.newArrayList(
                NodeType.WRITE_OFF.value()
        );
    }
}
