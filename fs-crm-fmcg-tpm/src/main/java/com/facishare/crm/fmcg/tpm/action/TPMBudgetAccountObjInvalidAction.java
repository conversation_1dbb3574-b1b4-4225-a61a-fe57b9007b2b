package com.facishare.crm.fmcg.tpm.action;

import com.facishare.crm.fmcg.common.apiname.TPMBudgetAccountFields;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.paas.I18N;
import com.facishare.crm.fmcg.tpm.business.abstraction.IBudgetAccountDetailService;
import com.facishare.crm.fmcg.tpm.service.abstraction.BuryModule;
import com.facishare.crm.fmcg.tpm.service.abstraction.BuryOperation;
import com.facishare.crm.fmcg.tpm.service.BuryService;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.predef.action.StandardInvalidAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;

/**
 * <AUTHOR>
 * @date 2022/6/30 下午5:39
 */
public class TPMBudgetAccountObjInvalidAction extends StandardInvalidAction {


    private static final IBudgetAccountDetailService budgetAccountDetailService = SpringUtil.getContext().getBean(IBudgetAccountDetailService.class);

    @Override
    protected void before(Arg arg) {
        super.before(arg);
        validateBudgetStatus();
    }

    private void validateBudgetStatus() {
        IObjectData data = this.dataList.get(0);
        String budgetStatus = data.get(TPMBudgetAccountFields.BUDGET_STATUS, String.class);
        if (TPMBudgetAccountFields.BUDGET_STATUS__ENABLE.equals(budgetStatus)) {
            throw new ValidateException(I18N.text(I18NKeys.BUDGET_ACCOUNT_OBJ_INVALID_ACTION_0));
        } else {
            if (budgetAccountDetailService.existBudgetDetail(actionContext.getTenantId(), data.getId())) {
                throw new ValidateException(I18N.text(I18NKeys.BUDGET_ACCOUNT_OBJ_INVALID_ACTION_1));
            }
        }
    }

    @Override
    protected Result after(Arg arg, Result result) {
        BuryService.asyncBudgetLog(actionContext.getTenantId(), actionContext.getUser().getUserIdInt(), BuryModule.Budget.BUDGET_ACCOUNT, BuryOperation.DELETE);
        return super.after(arg, result);
    }
}
