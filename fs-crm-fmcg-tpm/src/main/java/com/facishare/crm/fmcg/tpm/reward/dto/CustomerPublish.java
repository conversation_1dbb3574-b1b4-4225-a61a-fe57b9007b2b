package com.facishare.crm.fmcg.tpm.reward.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

public interface CustomerPublish {

    @Data
    @ToString
    class Arg implements Serializable {

        @JSO<PERSON>ield(name = "tenantId")
        @JsonProperty(value = "tenantId")
        @SerializedName("tenantId")
        private String tenantId;

        @JSONField(name = "dataId")
        @JsonProperty(value = "dataId")
        @SerializedName("dataId")
        private String dataId;

        @JSONField(name = "apiName")
        @JsonProperty(value = "apiName")
        @SerializedName("apiName")
        private String apiName;

    }

    @Data
    @Builder
    class Result implements Serializable {

        private Boolean success;

        @JSONField(name = "redPackId")
        @JsonProperty(value = "redPackId")
        @SerializedName("redPackId")
        private String redPackId;

        private String message;
    }
}