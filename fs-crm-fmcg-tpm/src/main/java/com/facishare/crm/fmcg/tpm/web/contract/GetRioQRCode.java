package com.facishare.crm.fmcg.tpm.web.contract;

import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

/**
 * Author: linmj
 * Date: 2024/1/9 16:29
 */
public interface GetRioQRCode {

    @Data
    @ToString
    class Arg implements Serializable {

        private String activityId;

        private String agreementId;
    }


    @Data
    @ToString
    class Result implements Serializable {

        private Boolean needShowImage;

        private String imageBase64;
    }
}
