package com.facishare.crm.fmcg.tpm.api.scan;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * Author: linmj
 * Date: 2023/9/20 14:16
 */
public interface BigDateScanCode {


    @Data
    @ToString
    class Arg implements Serializable {

        private String code;

        private String url;

        @JSONField(name = "app_id")
        @JsonProperty(value = "app_id")
        @SerializedName("app_id")
        private String appId;

        private String environment;

        @JSONField(name = "tenant_code")
        @JsonProperty(value = "tenant_code")
        @SerializedName("tenant_code")
        private String tenantCode;

    }


    @Data
    @ToString
    class Result implements Serializable {

        @JSONField(name = "product_info")
        @JsonProperty(value = "product_info")
        @SerializedName("product_info")
        private ProductInfoDTO productInfo;
    }
}
