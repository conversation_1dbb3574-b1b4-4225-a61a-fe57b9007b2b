package com.facishare.crm.fmcg.tpm.web.contract.model;

import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityProofAiConfigEntity;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

/**
 * AI display recognition configuration VO
 * <p>
 * Created for AI display recognition feature
 */
@Data
@ToString
public class ActivityProofAiConfigVO implements Serializable {

    /**
     * Enable/disable AI display recognition
     */
    @JSONField(name = "enable_ai_display_recognition")
    @JsonProperty(value = "enable_ai_display_recognition")
    @SerializedName("enable_ai_display_recognition")
    private Boolean enableAiDisplayRecognition;

    /**
     * Display recognition model
     */
    @JSONField(name = "display_recognition_model")
    @JsonProperty(value = "display_recognition_model")
    @SerializedName("display_recognition_model")
    private String displayRecognitionModel;

    /**
     * Adaptation rules
     */
    @JSONField(name = "adaptation_rule")
    @JsonProperty(value = "adaptation_rule")
    @SerializedName("adaptation_rule")
    private String adaptationRule;

    public static ActivityProofAiConfigVO fromPO(ActivityProofAiConfigEntity po) {
        if (po == null) {
            return null;
        }
        ActivityProofAiConfigVO vo = new ActivityProofAiConfigVO();
        vo.setEnableAiDisplayRecognition(po.getEnableAiDisplayRecognition());
        vo.setDisplayRecognitionModel(po.getDisplayRecognitionModel());
        vo.setAdaptationRule(po.getAdaptationRule());
        return vo;
    }
}