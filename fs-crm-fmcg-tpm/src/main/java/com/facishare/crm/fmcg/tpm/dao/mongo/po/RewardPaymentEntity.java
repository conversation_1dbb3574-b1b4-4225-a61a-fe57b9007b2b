package com.facishare.crm.fmcg.tpm.dao.mongo.po;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.mongodb.morphia.annotations.Embedded;

import java.io.Serializable;

/**
 * Author: linmj
 * Date: 2023/9/14 16:06
 */
@Data
@ToString
@EqualsAndHashCode
public class RewardPaymentEntity implements Serializable {


    public static final String F_PAY_ACCOUNT = "pay_account";
    public static final String F_RECEIVE_ACCOUNT = "receive_account";

    @Embedded(F_PAY_ACCOUNT)
    private RewardAccountEntity payAccount;

    @Embedded(F_RECEIVE_ACCOUNT)
    private RewardAccountEntity receiveAccount;

}
