package com.facishare.crm.fmcg.tpm.web.contract.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2022/10/14 15:22
 */
@Data
@ToString
public class AppendFreezeItemVO implements Serializable {

    @JSONField(name = "withholding_data_id")
    @JsonProperty(value = "withholding_data_id")
    @SerializedName("withholding_data_id")
    private String withholdingDataId;

    @JSONField(name = "source_account_id")
    @JsonProperty(value = "source_account_id")
    @SerializedName("source_account_id")
    private String sourceAccountId;

    @JSONField(name = "year")
    @JsonProperty(value = "year")
    @SerializedName("year")
    private int year;

    @JSONField(name = "month")
    @JsonProperty(value = "month")
    @SerializedName("month")
    private int month;

    @JSONField(name = "amount")
    @JsonProperty(value = "amount")
    @SerializedName("amount")
    private BigDecimal amount;
}
