package com.facishare.crm.fmcg.tpm.action;

import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.predef.action.StandardBulkInvalidAction;

/**
 * author: wuyx
 * description:
 * createTime: 2022/7/20 18:31
 */
public class TPMBudgetStatisticTableObjBulkInvalidAction extends StandardBulkInvalidAction {

    @Override
    protected void before(Arg arg) {
        super.before(arg);
        throw new ValidateException(I18N.text(I18NKeys.BUDGET_STATISTIC_TABLE_OBJ_BULK_INVALID_ACTION_0));
    }
}
