package com.facishare.crm.fmcg.tpm.session;

import com.facishare.crm.fmcg.tpm.session.model.SessionContent;
import com.facishare.qixin.api.constant.OSS1SubCategory;
import com.facishare.qixin.api.model.pushSession.arg.PushOSS1SessionArg;

import java.util.List;

/**
 * @author: wuyx
 * @description:
 * @createTime: 2022/1/7 10:42
 */
public interface SessionSendService {
    void pushOSS1SessionProjectManage(PushOSS1SessionArg arg);

    void pushOSS1Session(PushOSS1SessionArg arg);

    void sendTextMessageDefault(String message, String ea, List<Integer> employeeIds);

    void sendTextMessage(String message, String ea, List<Integer> employeeIds, OSS1SubCategory category);

    void doSendSession(String ea, List<Integer> receivers, SessionContent sessionContent, int flag);

    void doSendSessionToSystemAdmins(String tenantId, SessionContent sessionContent, int flag);

}
