package com.facishare.crm.fmcg.tpm.mq.model;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2020/8/25 5:51 PM
 */
@Data
@ToString
public class ApprovalEventOBJ implements Serializable {

    private String id;

    private String tag;

    private String eventType;

    /**
     * {"candidateIds":["1000"],"instanceId":"60e57e3901bd062a8898e509","dataId":"60d5aa48f343ad00010898e7","tenantId":"81783","entityId":"TPMDealerActivityCostObj","processedPersons":["1000"],"taskId":"60e57e397091b91708ada353"})
     */
    private JSONObject eventData;
}
