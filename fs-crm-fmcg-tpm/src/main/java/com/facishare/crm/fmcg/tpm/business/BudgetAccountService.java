package com.facishare.crm.fmcg.tpm.business;

import com.alibaba.fastjson.JSON;
import com.facishare.common.parallel.ParallelUtils;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.paas.I18N;
import com.google.common.collect.Lists;
import com.facishare.crm.fmcg.tpm.action.TPMBudgetAccountObjAddAction;
import com.facishare.crm.fmcg.tpm.action.TPMBudgetAccountObjEnableBudgetAction;
import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.common.apiname.CommonFields;
import com.facishare.crm.fmcg.common.apiname.TPMBudgetAccountFields;
import com.facishare.crm.fmcg.tpm.business.abstraction.IBudgetAccountService;
import com.facishare.crm.fmcg.tpm.common.constant.BudgetAccountConstants;
import com.facishare.crm.fmcg.tpm.dao.mongo.BudgetTypeDAO;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.*;
import com.facishare.crm.fmcg.tpm.utils.CommonUtils;
import com.facishare.crm.fmcg.tpm.utils.FormatUtil;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.core.predef.action.BaseObjectSaveAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.dao.pg.config.MetadataTransactional;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.impl.ui.layout.FieldSection;
import com.facishare.paas.metadata.impl.ui.layout.component.FormComponent;
import com.facishare.paas.metadata.ui.layout.*;
import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.bson.Document;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@SuppressWarnings("Duplicates")
public class BudgetAccountService implements IBudgetAccountService {

    @Resource
    private ServiceFacade serviceFacade;

    @Resource
    private BudgetTypeDAO budgetTypeDAO;

    @Override
    public void validate(
            String tenantId,
            BudgetTypePO type,
            BudgetTypeNodeEntity node,
            IObjectData budget) {

        SearchTemplateQuery stq = new SearchTemplateQuery();
        stq.setNeedReturnCountNum(false);
        stq.setLimit(1);
        stq.setOffset(0);
        stq.setSearchSource("db");

        Filter departmentFilter = new Filter();
        departmentFilter.setFieldName(TPMBudgetAccountFields.BUDGET_DEPARTMENT);
        departmentFilter.setOperator(Operator.EQ);
        departmentFilter.setFieldValues(CommonUtils.cast(budget.get(TPMBudgetAccountFields.BUDGET_DEPARTMENT), String.class));

        Filter typeFilter = new Filter();
        typeFilter.setFieldName(TPMBudgetAccountFields.BUDGET_TYPE_ID);
        typeFilter.setOperator(Operator.EQ);
        typeFilter.setFieldValues(Lists.newArrayList(type.getId().toString()));

        Filter nodeFilter = new Filter();
        nodeFilter.setFieldName(TPMBudgetAccountFields.BUDGET_NODE_ID);
        nodeFilter.setOperator(Operator.EQ);
        nodeFilter.setFieldValues(Lists.newArrayList(node.getNodeId()));

        Filter periodFilter = new Filter();
        String periodFieldApiName = String.format("budget_period_%s", node.getTimeDimension());
        periodFilter.setFieldName(periodFieldApiName);
        periodFilter.setOperator(Operator.EQ);
        periodFilter.setFieldValues(Lists.newArrayList(budget.get(periodFieldApiName, String.class)));

        stq.setFilters(Lists.newArrayList(departmentFilter, typeFilter, nodeFilter, periodFilter));


        List<BudgetDimensionEntity> dimensions = node.getDimensions();
        if (!CollectionUtils.isEmpty(dimensions)) {
            for (BudgetDimensionEntity dimensionEntity : dimensions) {
                Filter dimensionFilter = new Filter();
                String value = budget.get(dimensionEntity.getApiName(), String.class);
                if (Strings.isNullOrEmpty(value)) {
                    throw new ValidateException(I18N.text(I18NKeys.BUDGET_ACCOUNT_SERVICE_0) + dimensionEntity.getApiName() + I18N.text(I18NKeys.BUDGET_ACCOUNT_SERVICE_1));
                }
                dimensionFilter.setFieldName(dimensionEntity.getApiName());
                dimensionFilter.setOperator(Operator.EQ);
                dimensionFilter.setFieldValues(Lists.newArrayList(value));

                stq.getFilters().add(dimensionFilter);
            }
        }

        List<IObjectData> data = serviceFacade.findBySearchQueryWithFieldsIgnoreAll(User.systemUser(tenantId), ApiNames.TPM_BUDGET_ACCOUNT, stq, Lists.newArrayList("_id", "name")).getData();

        if (!data.isEmpty()) {
            throw new ValidateException(I18N.text(I18NKeys.BUDGET_ACCOUNT_SERVICE_2) + FormatUtil.join(data.stream().map(IObjectData::getName).collect(Collectors.toList()), ","));
        }
    }

    @Override
    public ILayout overrideLayout(ILayout layout, BudgetTypeNodeEntity node) {
        try {
            List<IComponent> components = layout.getComponents();
            components.stream().filter(component -> "form_component".equals(component.getName()) && "form".equals(component.getType()))
                    .findFirst().ifPresent(iComponent -> {
                        FormComponent formComponent = (FormComponent) iComponent;
                        List<IFieldSection> copyFieldSections = formComponent.getFieldSections();
                        IFieldSection dimensionFiledSection = getFieldSection(copyFieldSections, BudgetAccountConstants.BUDGET_EFFECTIVE_DIMENSION_SECTION);
                        IFieldSection periodFieldSection = getFieldSection(copyFieldSections, BudgetAccountConstants.BUDGET_PERIOD_SECTION);
                        List<IFieldSection> additionalSections = Lists.newArrayList(dimensionFiledSection, periodFieldSection);
                        List originalSections = formComponent.get(IFormComponent.FIELD_SECTIONS, List.class, new ArrayList<>());
                        List<String> removeList = new ArrayList<>();

                        List<Map<String, Object>> dimensionFormFields = dimensionFiledSection.get(IFormComponent.FIELD_SECTIONS, List.class, new ArrayList<>());
                        dimensionFiledSection.set(IFieldSection.FORM_FIELDS, dimensionFormFields);

                        List<Map<String, Object>> periodFormFields = periodFieldSection.get(IFormComponent.FIELD_SECTIONS, List.class, new ArrayList<>());
                        periodFieldSection.set(IFieldSection.FORM_FIELDS, periodFormFields);
                        if (node != null) {
                            if (Strings.isNullOrEmpty(node.getParentNodeId())) {
                                removeList.add(TPMBudgetAccountFields.PARENT_ID);
                            }

                            //维度处理
                            if (!CollectionUtils.isEmpty(node.getDimensions())) {
                                node.getDimensions().forEach(dimension -> {
                                    dimensionFormFields.add(getFormField(dimension.getApiName(), dimension.getType(), false, true));
                                    removeList.add(dimension.getApiName());
                                });
                            }

                            //期间处理
                            switch (BudgetPeriodEnum.of(node.getTimeDimension())) {
                                case QUARTER:
                                    periodFormFields.add(getFormField(TPMBudgetAccountFields.BUDGET_PERIOD_QUARTER, "date", false, true));
                                    break;
                                case MONTH:
                                    periodFormFields.add(getFormField(TPMBudgetAccountFields.BUDGET_PERIOD_MONTH, "date", false, true));
                                    break;
                                case YEAR:
                                    periodFormFields.add(getFormField(TPMBudgetAccountFields.BUDGET_PERIOD_YEAR, "date", false, true));
                                    break;
                                default:
                            }
                            removeList.addAll(Lists.newArrayList(TPMBudgetAccountFields.BUDGET_PERIOD_MONTH, TPMBudgetAccountFields.BUDGET_PERIOD_QUARTER, TPMBudgetAccountFields.BUDGET_PERIOD_YEAR));
                        }

                        //去除原有字段
                        removeFormSection(originalSections, removeList);

                        //删除
                        removeFieldSection(originalSections, additionalSections);
                        //插入
                        int insertIndex = 0;
                        while (insertIndex < formComponent.getFieldSections().size()) {
                            IFieldSection tmp = formComponent.getFieldSections().get(insertIndex);
                            if ("base_field_section__c".equals(tmp.getName())) {
                                break;
                            }
                            insertIndex++;
                        }
                        insertIndex = insertIndex == formComponent.getFieldSections().size() ? insertIndex - 1 : insertIndex + 1;
                        for (int index = 0; index < additionalSections.size(); index++) {
                            originalSections.add(insertIndex + index, Document.parse(additionalSections.get(index).toJsonString()));
                        }
                        formComponent.set(IFormComponent.FIELD_SECTIONS, originalSections);
                    });
        } catch (MetadataServiceException e) {
            throw new ValidateException(e.getMessage());
        }
        return layout;
    }

    @Override
    public List<IObjectData> queryRelatedBudgetByControlDimension(User user, String budgetType, IObjectData baseBudget) {

        String nodeId = baseBudget.get(TPMBudgetAccountFields.BUDGET_NODE_ID, String.class);
        BudgetTypeNodeEntity baseNode = budgetTypeDAO.getNodeById(user.getTenantId(), nodeId);

        ControlStrategyType controlStrategyType = ControlStrategyType.of(baseNode.getControlStrategy());
        if (!controlStrategyType.equals(ControlStrategyType.CUSTOM_DIMENSION_LIMIT)) {
            return Lists.newArrayList(baseBudget);
        }
        long budgetTimestamp = getBudgetEffectiveTimestamp(BudgetPeriodEnum.of(baseNode.getTimeDimension()), baseBudget);
        Long[] startAndEnd = getTimestamp(BudgetPeriodEnum.of(baseNode.getControlTimeDimension()), budgetTimestamp);
        long timeRangeStart = startAndEnd[0];
        long timeRangeEnd = startAndEnd[1];
        Map<String, IObjectData> budgetMap = new HashMap<>();


        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(-1);
        query.setOffset(0);
        query.setNeedReturnQuote(false);
        query.setNeedReturnCountNum(false);
        query.setSearchSource("db");

        List<IFilter> filters = new ArrayList<>();

        Filter templateTypeFilter = new Filter();
        templateTypeFilter.setFieldName(TPMBudgetAccountFields.BUDGET_TYPE_ID);
        templateTypeFilter.setOperator(Operator.EQ);
        templateTypeFilter.setFieldValues(Lists.newArrayList(budgetType));
        filters.add(templateTypeFilter);

        Filter nodeIdFilter = new Filter();
        nodeIdFilter.setFieldName(TPMBudgetAccountFields.BUDGET_NODE_ID);
        nodeIdFilter.setOperator(Operator.EQ);
        nodeIdFilter.setFieldValues(Lists.newArrayList(baseNode.getNodeId()));
        filters.add(nodeIdFilter);

        Filter budgetStatusFilter = new Filter();
        budgetStatusFilter.setFieldName(TPMBudgetAccountFields.BUDGET_STATUS);
        budgetStatusFilter.setOperator(Operator.EQ);
        budgetStatusFilter.setFieldValues(Lists.newArrayList(TPMBudgetAccountFields.BUDGET_STATUS__ENABLE));
        filters.add(budgetStatusFilter);

        Filter budgetLifeStatusFilter = new Filter();
        budgetLifeStatusFilter.setFieldName(CommonFields.LIFE_STATUS);
        budgetLifeStatusFilter.setOperator(Operator.EQ);
        budgetLifeStatusFilter.setFieldValues(Lists.newArrayList(CommonFields.LIFE_STATUS__NORMAL));
        filters.add(budgetLifeStatusFilter);

        Filter departmentFilter = new Filter();
        departmentFilter.setFieldName(TPMBudgetAccountFields.BUDGET_DEPARTMENT);
        departmentFilter.setOperator(Operator.EQ);
        departmentFilter.setFieldValues(baseBudget.get(TPMBudgetAccountFields.BUDGET_DEPARTMENT, List.class, Lists.newArrayList("-1")));
        filters.add(departmentFilter);


        Filter timeFilter = new Filter();
        filters.add(timeFilter);
        timeFilter.setOperator(Operator.BETWEEN);
        timeFilter.setFieldValues(Lists.newArrayList(String.valueOf(timeRangeStart), String.valueOf(timeRangeEnd - 1)));
        switch (BudgetPeriodEnum.of(baseNode.getTimeDimension())) {
            case QUARTER:
                timeFilter.setFieldName(TPMBudgetAccountFields.BUDGET_PERIOD_QUARTER);
                break;
            case MONTH:
                timeFilter.setFieldName(TPMBudgetAccountFields.BUDGET_PERIOD_MONTH);
                break;
            case YEAR:
                timeFilter.setFieldName(TPMBudgetAccountFields.BUDGET_PERIOD_YEAR);
        }

        if (!CollectionUtils.isEmpty(baseNode.getControlDimensions())) {
            baseNode.getControlDimensions().forEach(budgetDimensionEntity -> {
                Filter dimensionFilter = new Filter();
                dimensionFilter.setFieldName(budgetDimensionEntity.getApiName());
                dimensionFilter.setOperator(Operator.EQ);
                dimensionFilter.setFieldValues(Lists.newArrayList(baseBudget.get(budgetDimensionEntity.getApiName(), String.class, "-991999")));
                filters.add(dimensionFilter);
            });
        }
        query.setFilters(filters);
        CommonUtils.queryData(serviceFacade, user, ApiNames.TPM_BUDGET_ACCOUNT, query).forEach(v -> budgetMap.put(v.getId(), v));

        return new ArrayList<>(budgetMap.values());
    }

    @Override
    public List<IObjectData> querySonBudgets(User user, String parentId) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setNeedReturnCountNum(false);
        query.setNeedReturnQuote(false);
        query.setOffset(0);
        query.setLimit(-1);
        query.setSearchSource("db");

        List<IFilter> filters = Lists.newArrayList();
        Filter parentIdFilter = new Filter();
        parentIdFilter.setFieldName(TPMBudgetAccountFields.PARENT_ID);
        filters.add(parentIdFilter);

        if (!Strings.isNullOrEmpty(parentId)) {
            parentIdFilter.setOperator(Operator.EQ);
            parentIdFilter.setFieldValues(Lists.newArrayList(parentId));
        } else {
            parentIdFilter.setOperator(Operator.IS);
            parentIdFilter.setFieldValues(Lists.newArrayList());
        }

        Filter budgetStatusFilter = new Filter();
        budgetStatusFilter.setFieldName(TPMBudgetAccountFields.BUDGET_STATUS);
        budgetStatusFilter.setOperator(Operator.EQ);
        budgetStatusFilter.setFieldValues(Lists.newArrayList(TPMBudgetAccountFields.BUDGET_STATUS__ENABLE));
        filters.add(budgetStatusFilter);

        Filter budgetLifeStatusFilter = new Filter();
        budgetLifeStatusFilter.setFieldName(CommonFields.LIFE_STATUS);
        budgetLifeStatusFilter.setOperator(Operator.EQ);
        budgetLifeStatusFilter.setFieldValues(Lists.newArrayList(CommonFields.LIFE_STATUS__NORMAL));
        filters.add(budgetLifeStatusFilter);

        query.setFilters(filters);

        return CommonUtils.queryData(serviceFacade, user, ApiNames.TPM_BUDGET_ACCOUNT, query);
    }

    @Override
    public IObjectData getBudgetByConsumeRuleTemplateEntity(User user, String budgetType, BudgetTableNodeEntity budgetTableNodeEntity, IObjectData relatedData) {
        return getBudgetByFieldRelation(user, budgetType, budgetTableNodeEntity.getNodeId(), budgetTableNodeEntity.getFieldRelation(), relatedData);
    }

    @Override
    public IObjectData getBudgetByFieldRelation(User user, String budgetType, String nodeId, List<BudgetFieldRelationEntity> fieldRelationEntities, IObjectData relatedData) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setNeedReturnCountNum(false);
        query.setNeedReturnQuote(false);
        query.setOffset(0);
        query.setLimit(-1);
        query.setSearchSource("db");

        List<IFilter> filters = Lists.newArrayList();

        Filter templateTypeFilter = new Filter();
        templateTypeFilter.setFieldName(TPMBudgetAccountFields.BUDGET_TYPE_ID);
        templateTypeFilter.setOperator(Operator.EQ);
        templateTypeFilter.setFieldValues(Lists.newArrayList(budgetType));
        filters.add(templateTypeFilter);

        Filter nodeFilter = new Filter();
        nodeFilter.setFieldName(TPMBudgetAccountFields.BUDGET_NODE_ID);
        nodeFilter.setOperator(Operator.EQ);
        nodeFilter.setFieldValues(Lists.newArrayList(nodeId));
        filters.add(nodeFilter);

        Filter budgetStatusFilter = new Filter();
        budgetStatusFilter.setFieldName(TPMBudgetAccountFields.BUDGET_STATUS);
        budgetStatusFilter.setOperator(Operator.EQ);
        budgetStatusFilter.setFieldValues(Lists.newArrayList(TPMBudgetAccountFields.BUDGET_STATUS__ENABLE));
        filters.add(budgetStatusFilter);

        Filter budgetLifeStatusFilter = new Filter();
        budgetLifeStatusFilter.setFieldName(CommonFields.LIFE_STATUS);
        budgetLifeStatusFilter.setOperator(Operator.EQ);
        budgetLifeStatusFilter.setFieldValues(Lists.newArrayList(CommonFields.LIFE_STATUS__NORMAL));
        filters.add(budgetLifeStatusFilter);

        //时间字段得额外处理 一下 操作符可能是eql
        fieldRelationEntities.forEach(budgetFieldRelationEntity -> {
            Filter dimensionFilter = new Filter();
            dimensionFilter.setFieldName(budgetFieldRelationEntity.getSourceField());
            dimensionFilter.setOperator(Operator.valueOf(budgetFieldRelationEntity.getOperator().toUpperCase()));
            if (TPMBudgetAccountFields.BUDGET_DEPARTMENT.equals(budgetFieldRelationEntity.getSourceField())) {
                List<String> departments = CommonUtils.cast(relatedData.get(budgetFieldRelationEntity.getTargetField(), List.class, Lists.newArrayList("-1")), String.class);
                if (CollectionUtils.isEmpty(departments)) {
                    throw new ValidateException(I18N.text(I18NKeys.BUDGET_ACCOUNT_SERVICE_3));
                }
                dimensionFilter.setFieldValues(departments);
            } else {
                dimensionFilter.setFieldValues(Lists.newArrayList(relatedData.get(budgetFieldRelationEntity.getTargetField(), String.class, "-*********")));
            }
            filters.add(dimensionFilter);
        });

        query.setFilters(filters);

        List<IObjectData> data = CommonUtils.queryData(serviceFacade, user, ApiNames.TPM_BUDGET_ACCOUNT, query);
        log.info("search tmp:{},data:{}", JSON.toJSONString(query), data);
        return CollectionUtils.isEmpty(data) ? null : data.get(0);
    }


    @Override
    public IObjectData buildDefaultValForCreate() {

        IObjectData data = new ObjectData();
        data.set(TPMBudgetAccountFields.CALCULATE_APPLY_AMOUNT, 0.0D);
        data.set(TPMBudgetAccountFields.FROZEN_AMOUNT, 0.0D);
        data.set(TPMBudgetAccountFields.TRANSFER_IN_AMOUNT, 0.0D);
        data.set(TPMBudgetAccountFields.TRANSFER_OUT_AMOUNT, 0.0D);
        data.set(TPMBudgetAccountFields.USED_AMOUNT, 0.0D);
        data.setDescribeApiName(ApiNames.TPM_BUDGET_ACCOUNT);
        return data;
    }

    @Override
    @MetadataTransactional
    public IObjectData createBudgetAccount(User user, IObjectData account, boolean isTriggerApproval, boolean isTriggerWorkFlow, boolean isSonBudget, boolean skipBudgetStatistic) {
        ActionContext addActionContext = new ActionContext(RequestContext.builder().tenantId(user.getTenantId()).user(user).build(), ApiNames.TPM_BUDGET_ACCOUNT, "Add");
        addActionContext.setAttribute("triggerWorkflow", isTriggerWorkFlow);
        addActionContext.setAttribute("triggerFlow", isTriggerApproval);
        addActionContext.setAttribute("skipBudgetStatistic", skipBudgetStatistic);

        addActionContext.setAttribute(TPMBudgetAccountFields.IS_SON_BUDGET_FLAG, isSonBudget);

        BaseObjectSaveAction.Arg saveArg = new BaseObjectSaveAction.Arg();
        saveArg.setObjectData(ObjectDataDocument.of(account));
        saveArg.setSkipApprovalFlow(!isTriggerApproval);
        BaseObjectSaveAction.Result result = serviceFacade.triggerAction(addActionContext, saveArg, BaseObjectSaveAction.Result.class);
        return result.getObjectData().toObjectData();
    }

    @Override
    public void preValidateAccount(User user, IObjectData account, boolean isSonBudget) {
        ActionContext actionContext = new ActionContext(RequestContext.builder().tenantId(user.getTenantId()).user(user).build(), ApiNames.TPM_BUDGET_ACCOUNT, "Add");
        actionContext.setAttribute(TPMBudgetAccountFields.IS_SON_BUDGET_FLAG, isSonBudget);
        new TPMBudgetAccountObjAddAction().preValidate(account, actionContext);
    }

    @Override
    public void enableBudget(User user, IObjectData budget) {
        try {
            ActionContext newActionContext = new ActionContext(RequestContext.builder().tenantId(user.getTenantId()).user(user).build(), ApiNames.TPM_BUDGET_ACCOUNT, "EnableBudget");
            newActionContext.setAttribute("skipButtonConditions", true);
            newActionContext.setAttribute("skipBaseValidate", true);
            TPMBudgetAccountObjEnableBudgetAction.Arg enableArg = TPMBudgetAccountObjEnableBudgetAction.Arg.of(null, budget.getId());
            serviceFacade.triggerAction(newActionContext, enableArg, TPMBudgetAccountObjEnableBudgetAction.Result.class);
        } catch (Exception e) {
            Map<String, Object> updateMap = new HashMap<>();
            updateMap.put(TPMBudgetAccountFields.BUDGET_STATUS, TPMBudgetAccountFields.BUDGET_STATUS__DISABLE);
            serviceFacade.updateWithMap(user, budget, updateMap);
            throw e;
        }
    }

    @Override
    public String formControlDimensionCode(IObjectData budget, BudgetTypeNodeEntity budgetTypeNodeEntity) {
        String timeDimension = budget.get("budget_period_" + budgetTypeNodeEntity.getControlTimeDimension(), String.class);
        String departmentDimension = budget.get(TPMBudgetAccountFields.BUDGET_DEPARTMENT, String.class);
        StringBuilder codeBuilder = new StringBuilder();
        codeBuilder.append(timeDimension).append(":").append(departmentDimension);
        if (CollectionUtils.isNotEmpty(budgetTypeNodeEntity.getControlDimensions())) {
            budgetTypeNodeEntity.getControlDimensions().forEach(controlDimension -> {
                codeBuilder.append(":").append(budget.get(controlDimension.getApiName(), String.class));
            });
        }
        return DigestUtils.md5Hex(codeBuilder.toString());
    }

    private Long[] getTimestamp(BudgetPeriodEnum budgetPeriodEnum, long baseTime) {
        Long[] list = new Long[2];
        LocalDateTime localDate = LocalDateTime.ofInstant(Instant.ofEpochMilli(baseTime), ZoneId.systemDefault());
        switch (budgetPeriodEnum) {
            case YEAR:
                localDate = localDate.withDayOfYear(1).withHour(0).withMinute(0).withNano(0).withSecond(0);
                list[0] = localDate.toInstant(ZoneOffset.of("+8")).toEpochMilli();
                list[1] = localDate.plusYears(1).toInstant(ZoneOffset.of("+8")).toEpochMilli();
                break;
            case MONTH:
                localDate = localDate.withDayOfMonth(1).withHour(0).withMinute(0).withNano(0).withSecond(0);
                list[0] = localDate.toInstant(ZoneOffset.of("+8")).toEpochMilli();
                list[1] = localDate.plusMonths(1).toInstant(ZoneOffset.of("+8")).toEpochMilli();
                break;
            case QUARTER:
                localDate = localDate.withMonth((localDate.getMonthValue() + 2) / 3 * 3 - 2).withHour(0).withMinute(0).withNano(0).withSecond(0);
                list[0] = localDate.toInstant(ZoneOffset.of("+8")).toEpochMilli();
                list[1] = localDate.plusMonths(3).toInstant(ZoneOffset.of("+8")).toEpochMilli();
                break;
        }
        return list;
    }

    private Long getBudgetEffectiveTimestamp(BudgetPeriodEnum budgetPeriodEnum, IObjectData budget) {
        switch (budgetPeriodEnum) {
            case YEAR:
                return budget.get(TPMBudgetAccountFields.BUDGET_PERIOD_YEAR, Long.class);

            case MONTH:
                return budget.get(TPMBudgetAccountFields.BUDGET_PERIOD_MONTH, Long.class);

            case QUARTER:
                return budget.get(TPMBudgetAccountFields.BUDGET_PERIOD_QUARTER, Long.class);
            default:
                return 0L;
        }
    }


    private IFieldSection getFieldSection(List<IFieldSection> sections, BudgetAccountConstants.DefaultComponentFieldSection defaultComponentFieldSection) {
        for (IFieldSection section : sections) {
            if (defaultComponentFieldSection.getApiName().equals(section.getName())) {
                return section;
            }
        }
        IFieldSection fieldSection = new FieldSection();
        fieldSection.setShowHeader(defaultComponentFieldSection.getShowHeader());
        fieldSection.setHeader(I18N.text(defaultComponentFieldSection.getHeader()));
        fieldSection.setName(defaultComponentFieldSection.getApiName());
        fieldSection.set("column", defaultComponentFieldSection.getColumn());
        fieldSection.set("tab_index", defaultComponentFieldSection.getTabIndex());
        return fieldSection;
    }

    private void removeFieldSection(List originalSections, List<IFieldSection> fieldSections) {
        Iterator iterator = originalSections.iterator();
        while (iterator.hasNext()) {
            IFieldSection iFieldSection = new FieldSection((Map) iterator.next());
            for (IFieldSection section : fieldSections) {
                if (section.getName().equals(iFieldSection.getName())) {
                    iterator.remove();
                    break;
                }
            }
        }
    }

    private Map<String, Object> getFormField(String apiName, String type, Boolean readOnly, Boolean required) {
        Map<String, Object> formField = new HashMap<>();
        formField.put(IFormField.IS_READ_ONLY, readOnly);
        formField.put(IFormField.FIELD_NAME, apiName);
        formField.put(IFormField.RENDER_TYPE, type);
        formField.put(IFormField.IS_REQUIRED, required);
        return formField;
    }

    private void removeFormSection(List originalSections, List<String> apiNames) {
        for (Map originalSection : (Iterable<Map>) originalSections) {
            IFieldSection iFieldSection = new FieldSection(originalSection);
            List<Map<String, Object>> formFields = iFieldSection.get(IFieldSection.FORM_FIELDS, List.class, new ArrayList<>());
            Iterator<Map<String, Object>> formFiledIterator = formFields.iterator();
            while (formFiledIterator.hasNext()) {
                Map<String, Object> form = formFiledIterator.next();
                for (String apiName : apiNames) {
                    if (apiName.equals(form.get(IFormField.FIELD_NAME))) {
                        formFiledIterator.remove();
                        break;
                    }
                }
            }
        }
    }
}
