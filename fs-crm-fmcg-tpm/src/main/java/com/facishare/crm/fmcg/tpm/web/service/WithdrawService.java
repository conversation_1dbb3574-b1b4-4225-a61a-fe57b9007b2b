package com.facishare.crm.fmcg.tpm.web.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.converter.EIEAConverter;
import com.facishare.crm.fmcg.common.adapter.EnterpriseConnectionService;
import com.facishare.crm.fmcg.common.adapter.abstraction.IPayService;
import com.facishare.crm.fmcg.common.apiname.*;
import com.facishare.crm.fmcg.common.gray.TPMGrayUtils;
import com.facishare.crm.fmcg.common.http.ApiContext;
import com.facishare.crm.fmcg.common.http.ApiContextManager;
import com.facishare.crm.fmcg.common.utils.IdentityIdGenerator;
import com.facishare.crm.fmcg.common.utils.QueryDataUtil;
import com.facishare.crm.fmcg.tpm.api.MengNiuTenantInformation;
import com.facishare.crm.fmcg.tpm.api.withdraw.*;
import com.facishare.crm.fmcg.tpm.api.withdraw.dto.AlertRecordInfo;
import com.facishare.crm.fmcg.tpm.api.withdraw.dto.RewardPersonInfo;
import com.facishare.crm.fmcg.tpm.api.withdraw.dto.UserIdentityInfo;
import com.facishare.crm.fmcg.tpm.retry.setter.RedPacketWithdrawSetter;
import com.facishare.crm.fmcg.tpm.service.abstraction.IRoleService;
import com.facishare.crm.fmcg.tpm.service.abstraction.ITransactionProxy;
import com.facishare.crm.fmcg.tpm.service.abstraction.IWithdrawRecordService;
import com.facishare.crm.fmcg.tpm.utils.CommonUtils;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.crm.fmcg.tpm.utils.TimeUtils;
import com.facishare.crm.fmcg.tpm.web.service.abstraction.IWithdrawService;
import com.facishare.crm.privilege.model.valueobject.CrmException;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ObjectDefNotFoundError;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ObjectDescribeDocument;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.fxiaoke.enterpriserelation2.arg.UpstreamAndDownstreamOuterTenantIdOutArg;
import com.fxiaoke.enterpriserelation2.common.HeaderObj;
import com.fxiaoke.enterpriserelation2.common.RestResult;
import com.fxiaoke.enterpriserelation2.service.EnterpriseRelationService;
import com.github.autoconf.ConfigFactory;
import com.google.common.collect.Lists;
import de.lab4inf.math.util.Strings;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
//IgnoreI18nFile
@Slf4j
@Component
@SuppressWarnings("Duplicates,unused")
public class WithdrawService implements IWithdrawService {

    @Resource
    private ServiceFacade serviceFacade;

    @Resource
    private TenantHierarchyService tenantHierarchyService;

    @Resource
    private ITransactionProxy transactionProxy;

    @Resource
    private IPayService payService;

    @Resource
    private RedPacketWithdrawSetter redPacketWithdrawSetter;

    @Resource
    private IWithdrawRecordService withdrawRecordService;

    @Resource
    private EnterpriseConnectionService enterpriseConnectionService;

    @Resource
    private RedissonClient redissonCmd;

    @Resource(name = "tpmRoleService")
    private IRoleService roleService;

    @Autowired
    private EnterpriseRelationService enterpriseRelationService;

    @Resource
    private EIEAConverter eieaConverter;


    private static final String PREFIX = "withdraw-%s-%s-%s-%s";
    private static final String PRE_KEY = "withdraw:%s:rewardPersonId-%s";
    private static final String OPTION2 = "option2";
    private final List<String> RED_PACKET_FIELDS = Lists.newArrayList(
            CommonFields.ID, RedPacketRecordObjFields.REWARD_AMOUNT, RedPacketRecordObjFields.REWARDED_PERSON_ID,
            RedPacketRecordObjFields.TRANSFEREE_ID, RedPacketRecordObjFields.TRANSFEREE_PHONE,
            RedPacketRecordObjFields.TRANSFEREE_ACCOUNT_TYPE, RedPacketRecordObjFields.TRANSFEREE_WECHAT_OPEN_ID,
            RedPacketRecordObjFields.TRANSFEREE_WECHAT_APP_ID, RedPacketRecordObjFields.TRANSFEREE_NAME,
            RedPacketRecordObjFields.TRANSFEROR_ACCOUNT_TYPE, RedPacketRecordObjFields.TRANSFEROR_NAME,
            RedPacketRecordObjFields.TRANSFEROR_ID, RedPacketRecordObjFields.TRANSFEROR_PHONE,
            RedPacketRecordObjFields.TRANSFEROR_ACCOUNT, RedPacketRecordObjFields.TRANSFEROR_TENANT_ID
    );
    private static List<String> RED_PACKET_EXTEND_FIELDS;
    private static List<String> WITHDRAW_EXTEND_FIELDS;
    private static String LIMIT_AMOUNT;
    // 店老板角色 1 互联角色主负责人
    private static String IS_STORE_MASTER;
    private static String ALERT_CONFIG;
    private static String RED_PACKET_TITLE;
    private static Integer COUNT_DOWN_NUMS;

    static {
        ConfigFactory.getConfig("fs-fmcg-tpm-config", config -> {
            String configJson = config.get("withdraw_config");
            if (!Strings.isNullOrEmpty(configJson)) {
                JSONObject object = JSON.parseObject(configJson);
                LIMIT_AMOUNT = (String) object.getOrDefault("limit_amount", "0.1");
                IS_STORE_MASTER = (String) object.get("is_store_master");
                RED_PACKET_EXTEND_FIELDS = CommonUtils.castIgnore(object.get("red_packet_extend_fields"), String.class);
                WITHDRAW_EXTEND_FIELDS = CommonUtils.castIgnore(object.get("withdraw_extend_fields"), String.class);
            }
            ALERT_CONFIG = config.get("withdraw_alert_term_text");
            COUNT_DOWN_NUMS = config.getInt("withdraw_alert_count_down");
            RED_PACKET_TITLE = config.get("withdraw_red_packet_title");
        });
    }

    /**
     * 红包累计看板
     */
    @Override
    public Dashboard.Result dashboard(Dashboard.Arg arg) {
        ApiContext context = ApiContextManager.getContext();
        log.info("context : {}", context);
        //  当前人可能是企业人员，也可能会是 门店人员
        RewardPersonInfo rewardPersonInfo = getRewardPersonInfo(context.getTenantId(), context.getEmployeeId(), context.getOutTenantId(), context.getOutUserId());
        // 获取蒙牛的 1端企业
        String tenantId = getManufacturerTenantId(context.getTenantId());
        log.info("withdraw manufacturer tenantId is {}", tenantId);
        // 根据奖励人ID 去获取红包记录
        // 汇总红包记录累计金额
        BigDecimal totalAmount = summaryRedPacketRecordAmountByType(tenantId, null, rewardPersonInfo);

        // 汇总红包记录当日金额
        BigDecimal todayAmount = summaryRedPacketRecordAmountByType(tenantId, RedPacketStatusQueryEnum.TODAY.code(), rewardPersonInfo);

        // 汇总红包记录待提现金额
        BigDecimal availableWithdrawalAmount = summaryRedPacketRecordAmountByType(tenantId, RedPacketStatusQueryEnum.AWAIT.code(), rewardPersonInfo);
        Boolean confirmAgreement = findConfirmAgreement(tenantId, rewardPersonInfo);
        return Dashboard.Result.builder()
                .totalAmount(totalAmount)
                .todayAmount(todayAmount)
                .availableAmount(availableWithdrawalAmount)
                .isAvailable(availableWithdrawalAmount.compareTo(new BigDecimal(LIMIT_AMOUNT)) > 0)
                .unusualCount(0)
                .isNeedConfirmAgreement(confirmAgreement)
                .alertAgreementTerm(confirmAgreement ? getAlertAgreementTerm() : "")
                .countdownNums(COUNT_DOWN_NUMS == null ? 3 : COUNT_DOWN_NUMS)
                .build();
    }

    private String getAlertAgreementTerm() {
        if (!Strings.isNullOrEmpty(ALERT_CONFIG)){
            return ALERT_CONFIG;
        }
        return "<div style=\"line-height: 1.5; color:#545861;\"><h3 style=\"line-height: 200%; text-align: center; margin-bottom: 12rpx;\"><strong>警示告知书</strong></h3><p style=\"\">致：<u>尊敬的店主 /合作伙伴 /蒙牛员工</u></p><p style=\"text-indent: 2em;\" data-indent=\"1\">  鉴于本人在公司举办的<u>物码营销</u>项目活动（以下简称为“活动”）中作为工作人员接触包括但不限于营销活动规则、含现金返利的数字二维码等商业资料和保密信息（以下简称为“信息”），为了确保活动公平公正，切实保障消费者权益及公司利益，我承诺：</p><p style=\"text-indent: 2em;\" data-indent=\"1\">  1、严格遵守国家、地方、行业等相关营销活动的法律法规、标准和公司规章制度等规定，确保活动公平公正。对上述活动所涉信息承担最高级别的保密义务：</p><p style=\"text-indent: 2em;\" data-indent=\"1\">  （1）所有信息只能用于本人职责范围内执行活动之目的，未得到公司的事先书面允许，不得以泄露、告知、公布、发布、出版、传授、转让或者其他任何方式直接或者间接将任何信息泄露或者用于其他目的。</p><p style=\"text-indent: 2em;\" data-indent=\"1\">  （2）本人承诺本着谨慎、诚实的原则，采取任何必要合理的措施保管所知悉的信息。</p><p style=\"text-indent: 2em;\" data-indent=\"1\">  （3）如因职责调整不再参与活动执行，本人应立即向公司归还所有信息资料及其载体，且不得留下任何形式的复制件，如果因信息介质所限，无法返还，则在公司同意后，销毁该等信息。</p><p style=\"text-indent: 2em;\" data-indent=\"1\">  2、认真严格履行岗位职责，及时学习岗位职责相关的活动执行规章制度及注意事项并严格落实，且做好相关工作记录。</p><p style=\"text-indent: 2em;\" data-indent=\"1\">  3、严守纪律，绝不触碰违法违规红线（包括但不限于组织或者参与黑灰产盗卖二维码等利用职务之便获取不正当利益），接受公司监管工作人员的监管，积极配合公司处置异常活动事件、追查监督检查工作。</p><p style=\"text-indent: 2em;\" data-indent=\"1\">  如果违反以上承诺内容或者公司的红线管理相关要求，本人清楚公司将依据制度（《企业纪律处分工作办法》、《员工奖惩制度》等）进行考核。若由于本人未能履行保密义务或者勤勉义务而对公司或者其他任何第三方造成任何损失，本人应向相应的受损方赔偿因此造成的全部损失，本人接受并配合公司采取的包括但不限于追究涉案个人的刑事、行政、民事责任的措施。</p><p style=\"text-indent: 2em;\" data-indent=\"1\">（以下无正文）</p></div>";
    }

    private Boolean findConfirmAgreement(String tenantId, RewardPersonInfo rewardPersonInfo) {

        // 没有自定义红包警示记录的企业，直接返回，不弹框。
        try {
            serviceFacade.findObject(tenantId, RedPacketAlertRecordFields.DESCRIBE_API_NAME);
        }catch (ObjectDefNotFoundError ex){
            log.info("red_packet_alert_record__c object not found, tenant id: {}", tenantId);
            return false;
        }

        // 查询自定义对象协议确认的数据，未找到表示 未确认协议，需要弹框
        // 查询
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(1);
        query.setOffset(0);

        List<IFilter> filters = new ArrayList<>();

        // 根据人员筛选
        Filter personFilter = new Filter();
        personFilter.setFieldName(RedPacketAlertRecordFields.REWARD_PERSON_ID);
        personFilter.setOperator(Operator.EQ);
        personFilter.setFieldValues(Lists.newArrayList(rewardPersonInfo.getRewardPersonId()));
        filters.add(personFilter);

        //如果门店 id 存在，加条件
        String storeId = rewardPersonInfo.getStoreId();
        if (!Strings.isNullOrEmpty(storeId)) {
            Filter storeFilter = new Filter();
            storeFilter.setFieldName(RedPacketAlertRecordFields.STORE_ID);
            storeFilter.setOperator(Operator.EQ);
            storeFilter.setFieldValues(Lists.newArrayList(rewardPersonInfo.getStoreId()));
            filters.add(storeFilter);
        }
        query.setFilters(filters);
        return serviceFacade.findBySearchQuery(User.systemUser(tenantId),RedPacketAlertRecordFields.DESCRIBE_API_NAME,query).getTotalNumber() == 0;
    }

    private Integer getRedPacketRecordTotalByErrorMessage(String tenantId, RewardPersonInfo rewardPersonInfo) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(1);
        query.setOffset(0);
        List<IFilter> recordByTypeFilter = new ArrayList<>();
        buildFilterByPerson(rewardPersonInfo, recordByTypeFilter);
        // 按参数的筛选条件
        buildFilterByRedPacketStatus(RedPacketStatusQueryEnum.GRANT_FAIL.code(), null, recordByTypeFilter);

        Filter paymentErrorMessagefilter = new Filter();
        paymentErrorMessagefilter.setFieldName(RedPacketRecordObjFields.PAYMENT_ERROR_MESSAGE);
        paymentErrorMessagefilter.setOperator(Operator.IN);
        paymentErrorMessagefilter.setFieldValues(getPaymentErrorMessage());
        recordByTypeFilter.add(paymentErrorMessagefilter);

        query.setFilters(recordByTypeFilter);

        return serviceFacade.findBySearchQuery(User.systemUser(tenantId), ApiNames.RED_PACKET_RECORD_OBJ, query).getTotalNumber();


    }

    private String getStoreIdByOutTenantId(String tenantId, String outTenantId, String outUserId) {

        UpstreamAndDownstreamOuterTenantIdOutArg upstreamAndDownstreamOuterTenantIdOutArg = new UpstreamAndDownstreamOuterTenantIdOutArg();
        upstreamAndDownstreamOuterTenantIdOutArg.setDownstreamOuterTenantId(Long.valueOf(outTenantId));
        upstreamAndDownstreamOuterTenantIdOutArg.setObjectApiName(ApiNames.ACCOUNT_OBJ);
        upstreamAndDownstreamOuterTenantIdOutArg.setUpstreamEa(eieaConverter.enterpriseIdToAccount(Integer.parseInt(tenantId)));
        RestResult<String> mapper = enterpriseRelationService.getMapperObjectId(HeaderObj.newInstance(Integer.valueOf(tenantId)), upstreamAndDownstreamOuterTenantIdOutArg);
        if (mapper.isSuccess()) {
            return mapper.getData();
        } else {
            throw new MetaDataBusinessException(mapper.getErrMsg());
        }
    }

    private String validatePersonStoreMasterRole(String tenantId, String outTenantId, String outUserId) {

        IObjectData employeeObj = serviceFacade.findObjectDataIgnoreAll(User.systemUser(tenantId), outUserId, ApiNames.PUBLIC_EMPLOYEE_OBJ);
        if (employeeObj != null) {
            Boolean isMaster = employeeObj.get(PublicEmployeeFields.RELATION_OWNER, Boolean.class);
            // 联系人id
            isMaster = isContractStoreMaster(tenantId, employeeObj, isMaster);
            return Boolean.TRUE.equals(isMaster) ? "1" : "0";
        }
        return null;
    }

    private Boolean isContractStoreMaster(String tenantId, IObjectData employeeObj, Boolean isMaster) {
        String contractId = employeeObj.get(PublicEmployeeFields.CONTRACT_ID, String.class);
        if (Strings.isNullOrEmpty(contractId)) {
            return isMaster;
        }
        IObjectData contactObj = serviceFacade.findObjectDataIgnoreAll(User.systemUser(tenantId), contractId, ApiNames.CONTACT_OBJ);
        if (contactObj != null) {
            String personType = contactObj.get("field_2m89p__c", String.class);
            if (!Strings.isNullOrEmpty(personType)) {
                // field_2m89p__c 人员类型，1：店主
                isMaster = "1".equals(personType);
            }
        }
        return isMaster;
    }

    private UserIdentityInfo validatePersonIdCard(String tenantId, Integer employeeId, String outTenantId, String outUserId) {
        String idCard = "", name = "", phone = "", wxOpenId = "", wxAppId = "";
        if (!Strings.isNullOrEmpty(outTenantId) && !Strings.isNullOrEmpty(outUserId)) {
            IObjectData contact = enterpriseConnectionService.getContactObjByPublicEmployeeId(tenantId, outUserId);
            if (contact != null) {
                idCard = contact.get(ContactFields.MENGNIU_ID_CARD_NUMBER, String.class);
                name = contact.get(ContactFields.NAME, String.class);
                phone = contact.get(ContactFields.MOBILE1, String.class);
                wxOpenId = contact.get(ContactFields.MENGNIU_WECHAT_OPEN_ID, String.class);
                wxAppId = contact.get(ContactFields.MENGNIU_WECHAT_APP_ID, String.class);
            }
        } else {
            IObjectData user = serviceFacade.findObjectDataIgnoreAll(User.systemUser(tenantId), String.valueOf(employeeId), ApiNames.PERSONNEL_OBJ);
            if (user != null) {
                idCard = user.get(PersonnelFields.MENGNIU_IDCARD, String.class);
                name = user.get(PersonnelFields.FULL_NAME, String.class);
                phone = user.get(PersonnelFields.PHONE, String.class);
                wxOpenId = user.get(PersonnelFields.MENGNIU_WECHAT_OPEN_ID, String.class);
                wxAppId = user.get(PersonnelFields.MENGNIU_WECHAT_APP_ID, String.class);
            }
        }
        log.info("login person idCard is {}", idCard);
        if (Strings.isNullOrEmpty(idCard)) {
            throw new ValidateException(I18N.text(I18NKeys.WITHDRAW_GET_USERINFO_ID_CARD_ERROR));
        }
        return UserIdentityInfo.builder().idCard(idCard).name(name).phone(phone).wxOpenId(wxOpenId).wxAppId(wxAppId).build();
    }

    private String getManufacturerTenantId(String tenantId) {
        MengNiuTenantInformation tenant = tenantHierarchyService.load(tenantId);
        if (MengNiuTenantInformation.ROLE_OTHERS.equals(tenant.getRole())) {
            if (TPMGrayUtils.isSkipMengNiuTenantCheck(tenantId)){
                return Objects.isNull(tenant.getManufacturer()) ? tenantId : tenant.getManufacturer().getTenantId();
            }
            throw new ValidateException(I18N.text(I18NKeys.WITHDRAW_TENANT_NOT_UNDER_THE_MENGNIU));
        }

        return tenant.getManufacturer().getTenantId();
    }

    private BigDecimal summaryRedPacketRecordAmountByType(String tenantId, String redPacketStatus, RewardPersonInfo rewardPersonInfo) {

        return summaryRedPacketRecordAmountByTypeWithRecordIds(tenantId, redPacketStatus, rewardPersonInfo, null, null);
    }

    private BigDecimal summaryRedPacketRecordAmountByTypeWithRecordIds(String tenantId,
                                                                       String redPacketStatus,
                                                                       RewardPersonInfo rewardPersonInfo,
                                                                       List<String> whiteIds,
                                                                       List<String> blackIds) {
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setSearchSource("db");
        query.setLimit(1);
        query.setOffset(0);

        List<IFilter> recordByTypeFilter = Lists.newArrayList();
        buildFilterByPerson(rewardPersonInfo, recordByTypeFilter);
        // 按参数的筛选条件
        buildFilterByRedPacketStatus(redPacketStatus, null, recordByTypeFilter);

        if (CollectionUtils.isNotEmpty(whiteIds)) {
            Filter recordIdsFilter = new Filter();
            recordIdsFilter.setFieldName(CommonFields.ID);
            recordIdsFilter.setOperator(Operator.IN);
            recordIdsFilter.setFieldValues(Lists.newArrayList(whiteIds));
            recordByTypeFilter.add(recordIdsFilter);
        }
        if (CollectionUtils.isNotEmpty(blackIds)) {
            Filter recordIdsFilter = new Filter();
            recordIdsFilter.setFieldName(CommonFields.ID);
            recordIdsFilter.setOperator(Operator.NIN);
            recordIdsFilter.setFieldValues(Lists.newArrayList(blackIds));
            recordByTypeFilter.add(recordIdsFilter);
        }

        query.setFilters(recordByTypeFilter);

        // 人员 按照 奖励人范围统计
        String groupByField = RedPacketRecordObjFields.REWARDED_PERSON_ID;
        // 店主按照 门店范围统计
        if (IS_STORE_MASTER.equals(rewardPersonInfo.getStoreMasterRole())) {
            groupByField = RedPacketRecordObjFields.ACCOUNT_ID;
        }

        List<IObjectData> data = serviceFacade.aggregateFindBySearchQueryWithGroupFields(
                User.systemUser(tenantId),
                query,
                ApiNames.RED_PACKET_RECORD_OBJ,
                Lists.newArrayList(groupByField),
                "sum",
                RedPacketRecordObjFields.REWARD_AMOUNT
        );

        if (!CollectionUtils.isEmpty(data)) {
            BigDecimal amount = data.get(0).get("sum_" + RedPacketRecordObjFields.REWARD_AMOUNT, BigDecimal.class);
            return Objects.isNull(amount) ? BigDecimal.ZERO : amount;
        } else {
            return new BigDecimal("0");
        }
    }


    private String getStart(long time) {
        return String.valueOf(TimeUtils.convertToDayStart(time));
    }

    private String getEnd(long time) {
        return String.valueOf(TimeUtils.convertToDayEnd(time));
    }

    /**
     * 红包记录列表
     *
     * @param arg
     * @return
     */
    @Override
    public QueryRedPacketRecords.Result queryRedPacketRecords(QueryRedPacketRecords.Arg arg) {
        ApiContext context = ApiContextManager.getContext();
        log.info("context : {}", context);
        // 当前人可能是企业人员，也可能会是 门店人员
        RewardPersonInfo rewardPersonInfo = getRewardPersonInfo(context.getTenantId(), context.getEmployeeId(), context.getOutTenantId(), context.getOutUserId());

        // 获取蒙牛的 1端企业
        String tenantId = getManufacturerTenantId(context.getTenantId());
        QueryRedPacketRecords.Result result = new QueryRedPacketRecords.Result();
        log.info("queryRedPacketRecords pageSize : {}, pageIndex : {}", arg.getPageSize(), arg.getPageIndex());
        // 查询红包记录数据  reward_person_id = tenantId.userId  通过奖励人去获取可提现的红包记录。
        QueryResult<IObjectData> redPacketRecordData = queryRedPacketRecordsByType(tenantId,
                arg.getRedPacketStatus(), arg.getDateTime(), rewardPersonInfo, arg.getLastItem(), arg.getPageSize(),
                arg.getPageSize() * arg.getPageIndex());
        if (CollectionUtils.isEmpty(redPacketRecordData.getData())) {
            result.setData(new ArrayList<>());
            result.setTotal(0);
            return result;
        }
        IObjectDescribe describe = serviceFacade.findObject(tenantId, ApiNames.RED_PACKET_RECORD_OBJ);
        fillRedPackRecordData(tenantId, redPacketRecordData.getData(), describe);
        result.setData(ObjectDataDocument.ofList(redPacketRecordData.getData()));
        result.setTotal(getRedPacketRecordTotal(tenantId, arg.getRedPacketStatus(), arg.getDateTime(), rewardPersonInfo));
        // 动作，活动，时间，状态， 金额，过期时间  配置读取简易布局、补充字段
        if (arg.isNeedSimpleLayout()) {
            String title = Strings.isNullOrEmpty(RED_PACKET_TITLE) ? RedPacketRecordObjFields.ACTIVITY_ID : RED_PACKET_TITLE;
            JSONObject simpleLayout = buildSimpleListLayout(
                    title,
                    RedPacketRecordObjFields.REWARD_AMOUNT,
                    CommonFields.CREATE_TIME,
                    RedPacketRecordObjFields.RED_PACKET_QUERY_STATUS,
                    RED_PACKET_EXTEND_FIELDS);
            simpleLayout.put("duration_days", RedPacketRecordObjFields.DURATION_DAYS);
            result.setSimpleListLayout(simpleLayout);
            result.setDescribe(ObjectDescribeDocument.of(describe));
        }
        // 返回可提现总金额--前端要用
        if (arg.isNeedTotalAmount()) {
            result.setAmount(summaryRedPacketRecordAmountByType(tenantId, RedPacketStatusQueryEnum.AWAIT.code(), rewardPersonInfo));
        }


        return result;
    }

    private Integer getRedPacketRecordTotal(String tenantId,
                                            String redPacketStatus,
                                            Long dateTime,
                                            RewardPersonInfo rewardPersonInfo) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(1);
        query.setOffset(0);
        List<IFilter> recordByTypeFilter = new ArrayList<>();
        buildFilterByPerson(rewardPersonInfo, recordByTypeFilter);
        // 按参数的筛选条件
        buildFilterByRedPacketStatus(redPacketStatus, dateTime, recordByTypeFilter);

        query.setFilters(recordByTypeFilter);

        return serviceFacade.findBySearchQuery(User.systemUser(tenantId), ApiNames.RED_PACKET_RECORD_OBJ, query).getTotalNumber();
    }

    private void fillRedPackRecordData(String tenantId, List<IObjectData> data, IObjectDescribe describe) {
        // 关联字段-补全信息
        serviceFacade.fillObjectDataWithRefObject(describe, data, User.systemUser(tenantId), null, false);

        for (IObjectData redPacketData : data) {
            String withdrawStatus = redPacketData.get(RedPacketRecordObjFields.WITHDRAWAL_STATUS, String.class);
            String redPacketStatus = redPacketData.get(RedPacketRecordObjFields.RED_PACKET_STATUS, String.class);
            String paymentStatus = redPacketData.get(RedPacketRecordObjFields.PAYMENT_STATUS, String.class);
            String paymentErrorMessage = redPacketData.get(RedPacketRecordObjFields.PAYMENT_ERROR_MESSAGE, String.class);
            RedPacketStatusQueryEnum packetStatus = RedPacketStatusQueryEnum.getRedPacketStatus(withdrawStatus, redPacketStatus, paymentStatus);
            redPacketData.set(RedPacketRecordObjFields.RED_PACKET_QUERY_STATUS, packetStatus.code());
            if (paymentErrorMessage != null && !getPaymentErrorMessage().contains(paymentErrorMessage)) {
                if ("业务服务费余额不足".equals(paymentErrorMessage)){
                    redPacketData.set(RedPacketRecordObjFields.PAYMENT_ERROR_MESSAGE, null);
                }else {
                    redPacketData.set(RedPacketRecordObjFields.PAYMENT_ERROR_MESSAGE, "因系统异常，本次转账失败");
                }
            } else {
                Long expirationTime = redPacketData.get(RedPacketRecordObjFields.EXPIRATION_TIME, Long.class);
                if (RedPacketStatusQueryEnum.AWAIT.equals(packetStatus) && Objects.nonNull(expirationTime)) {
                    long durationDays = TimeUtils.calculateDurationDays(System.currentTimeMillis(), expirationTime);
                    redPacketData.set(RedPacketRecordObjFields.DURATION_DAYS, durationDays);
                }
            }
            // 动态关联字段为空、默认为活动激励、关联活动。
            fillReferenceField(redPacketData);
        }

    }

    private static void fillReferenceField(IObjectData redPacketData) {

        try {
            String noteDataName = redPacketData.get(RedPacketRecordObjFields.REFERENCE_NOTE_API_NAME, String.class);
            String noteDataId = redPacketData.get(RedPacketRecordObjFields.REFERENCE_NOTE_DATA_ID, String.class);
            String activityId = redPacketData.get(RedPacketRecordObjFields.ACTIVITY_ID, String.class);
            String activityName = redPacketData.get(RedPacketRecordObjFields.ACTIVITY_ID__R, String.class);
            if (Strings.isNullOrEmpty(noteDataName) || Strings.isNullOrEmpty(noteDataId)){
                redPacketData.set(RedPacketRecordObjFields.REFERENCE_NOTE_DATA_ID__R, activityName);
                redPacketData.set(RedPacketRecordObjFields.REFERENCE_NOTE_DATA_ID, activityId);
                redPacketData.set(RedPacketRecordObjFields.REFERENCE_NOTE_API_NAME, ApiNames.TPM_ACTIVITY_OBJ);
            }
        }catch (Exception ex){
            log.error("fillReferenceField error", ex);
        }
    }

    private QueryResult<IObjectData> queryRedPacketRecordsByType(String tenantId,
                                                                 String redPacketStatus,
                                                                 Long dateTime,
                                                                 RewardPersonInfo rewardPersonInfo,
                                                                 String lastItem,
                                                                 int limit, int offset) {
        SearchTemplateQuery query = new SearchTemplateQuery();
//        query.setSearchSource("db");
        query.setLimit(limit);
        if (Strings.isNullOrEmpty(lastItem)) {
            query.setOffset(offset);
        }
        query.setNeedReturnCountNum(false);

        OrderBy order = new OrderBy();
        order.setFieldName(CommonFields.ID);
        order.setIsAsc(false);
        query.setOrders(Lists.newArrayList(order));

        List<IFilter> recordByTypeFilter = new ArrayList<>();
        buildFilterByPerson(rewardPersonInfo, recordByTypeFilter);
        // 按参数的筛选条件
        buildFilterByRedPacketStatus(redPacketStatus, dateTime, recordByTypeFilter);

        if (!Strings.isNullOrEmpty(lastItem)) {
            Filter idIndexFilter = new Filter();
            idIndexFilter.setFieldName(CommonFields.ID);
            idIndexFilter.setOperator(Operator.LT);
            idIndexFilter.setFieldValues(Lists.newArrayList(lastItem));
            recordByTypeFilter.add(idIndexFilter);
        }

        query.setFilters(recordByTypeFilter);
        return serviceFacade.findBySearchQuery(User.systemUser(tenantId), ApiNames.RED_PACKET_RECORD_OBJ, query);
    }

    private void buildFilterByRedPacketStatus(String redPacketStatus,
                                              Long dateTime,
                                              List<IFilter> recordByTypeFilter) {

        if (Objects.nonNull(dateTime)) {
            // 根据入参日期 筛选
            Filter dateTimeFilter = new Filter();
            dateTimeFilter.setFieldName(CommonFields.CREATE_TIME);
            dateTimeFilter.setOperator(Operator.BETWEEN);
            dateTimeFilter.setFieldValues(Lists.newArrayList(getStart(dateTime), getEnd(dateTime)));
            recordByTypeFilter.add(dateTimeFilter);
        }

        if (!Strings.isNullOrEmpty(redPacketStatus) && RedPacketStatusQueryEnum.codeOf(redPacketStatus) != null) {
            if (RedPacketStatusQueryEnum.AWAIT.code().equals(redPacketStatus)) {
                // 提现状态待提现
                Filter withdrawalStatusFilter = new Filter();
                withdrawalStatusFilter.setFieldName(RedPacketRecordObjFields.WITHDRAWAL_STATUS);
                withdrawalStatusFilter.setOperator(Operator.EQ);
                withdrawalStatusFilter.setFieldValues(Lists.newArrayList(RedPacketRecordObjFields.WithdrawalStatus.AWAIT));
                recordByTypeFilter.add(withdrawalStatusFilter);
                // 红包状态 = 生效中
                Filter redPacketStatusFilter = new Filter();
                redPacketStatusFilter.setFieldName(RedPacketRecordObjFields.RED_PACKET_STATUS);
                redPacketStatusFilter.setOperator(Operator.EQ);
                redPacketStatusFilter.setFieldValues(Lists.newArrayList(RedPacketRecordObjFields.RedPacketStatus.EFFECTUATE));
                recordByTypeFilter.add(redPacketStatusFilter);
            } else if (RedPacketStatusQueryEnum.TODAY.code().equals(redPacketStatus)) {
                Filter todayFilter = new Filter();
                todayFilter.setFieldName(CommonFields.CREATE_TIME);
                todayFilter.setOperator(Operator.GTE);
                todayFilter.setFieldValues(Lists.newArrayList(getStart(System.currentTimeMillis())));
                recordByTypeFilter.add(todayFilter);
            } else if (RedPacketStatusQueryEnum.EXPIRED.code().equals(redPacketStatus)) {
                Filter redPacketStatusFilter = new Filter();
                redPacketStatusFilter.setFieldName(RedPacketRecordObjFields.RED_PACKET_STATUS);
                redPacketStatusFilter.setOperator(Operator.EQ);
                redPacketStatusFilter.setFieldValues(Lists.newArrayList(RedPacketRecordObjFields.RedPacketStatus.EXPIRED));
                recordByTypeFilter.add(redPacketStatusFilter);
            } else {
                if (RedPacketStatusQueryEnum.GRANT_FAIL.code().equals(redPacketStatus)) {
                    Filter paymentErrorStatusfilter = new Filter();
                    paymentErrorStatusfilter.setFieldName(RedPacketRecordObjFields.PAYMENT_STATUS);
                    paymentErrorStatusfilter.setOperator(Operator.IN);
                    paymentErrorStatusfilter.setFieldValues(getPaymentErrorStatus());
                    recordByTypeFilter.add(paymentErrorStatusfilter);
                } else {
                    Filter withdrawalStatusFilter = new Filter();
                    withdrawalStatusFilter.setFieldName(RedPacketRecordObjFields.PAYMENT_STATUS);
                    withdrawalStatusFilter.setOperator(Operator.EQ);
                    withdrawalStatusFilter.setFieldValues(getRedPaymentStatus(redPacketStatus));
                    recordByTypeFilter.add(withdrawalStatusFilter);

                }
            }
        }
    }

    private List<String> getPaymentErrorStatus() {
        return Lists.newArrayList(RedPacketRecordObjFields.PaymentStatus.FAIL, RedPacketRecordObjFields.PaymentStatus.ERROR);
    }

    private List<String> getPaymentErrorMessage() {

        return Lists.newArrayList("身份证姓名不匹配", "身份证号错误","云支付失败，原因：姓名|身份证|金额不允许为空",
            "云支付失败，原因：微信用户OpenID不允许为空");
    }

    private List<String> getRedPaymentStatus(String redPacketStatus) {
        List<String> paymentList = new ArrayList<>();
        if (RedPacketStatusQueryEnum.WITHDRAWN.code().equals(redPacketStatus)) {
            paymentList.add(RedPacketRecordObjFields.PaymentStatus.SUCCESS);
        } else if (RedPacketStatusQueryEnum.WITHDRAWING.code().equals(redPacketStatus)) {
            paymentList.add(RedPacketRecordObjFields.PaymentStatus.TRANSFERRING);
        }
        return paymentList;
    }


    /**
     * 提现记录列表
     *
     * @param arg
     * @return
     */
    @Override
    public QueryWithdrawRecords.Result queryWithdrawRecords(QueryWithdrawRecords.Arg arg) {
        ApiContext context = ApiContextManager.getContext();
        log.info("context : {}", context);
        // 当前人可能是企业人员，也可能会是 门店人员
        RewardPersonInfo rewardPersonInfo = getRewardPersonInfo(context.getTenantId(), context.getEmployeeId(), context.getOutTenantId(), context.getOutUserId());

        // 获取蒙牛的 1端企业
        String tenantId = getManufacturerTenantId(context.getTenantId());
        QueryWithdrawRecords.Result result = new QueryWithdrawRecords.Result();
        // 查看红包提现记录
        QueryResult<IObjectData> withdrawRecordsData = queryWithdrawRecordsByParam(tenantId,
                arg.getWithdrawStatus(), arg.getDateTime(), rewardPersonInfo, arg.getPageSize(), arg.getPageSize() * arg.getPageIndex());
        if (CollectionUtils.isEmpty(withdrawRecordsData.getData())) {
            result.setData(new ArrayList<>());
            result.setTotal(0);
            return result;
        }
        fillWithdrawRecordData(tenantId, withdrawRecordsData.getData());
        result.setData(ObjectDataDocument.ofList(withdrawRecordsData.getData()));
        result.setTotal(withdrawRecordsData.getTotalNumber());
        //配置读取简易布局、补充字段
        if (arg.isNeedSimpleLayout()) {
            JSONObject simpleLayout = buildSimpleListLayout(
                    CommonFields.NAME,
                    WithdrawRecordObjFields.WITHDRAWAL_AMOUNT,
                    WithdrawRecordObjFields.WITHDRAWAL_OPERATION_TIME,
                    WithdrawRecordObjFields.WITHDRAW_QUERY_STATUS,
                    WITHDRAW_EXTEND_FIELDS);
            result.setSimpleListLayout(simpleLayout);
            IObjectDescribe desc = serviceFacade.findObject(tenantId, ApiNames.WITHDRAW_RECORD_OBJ);
            result.setDescribe(ObjectDescribeDocument.of(desc));

        }
        return result;
    }

    @NotNull
    private JSONObject buildSimpleListLayout(String title,
                                             String amount,
                                             String dateTime,
                                             String status,
                                             List<String> fields) {
        JSONObject simpleLayout = new JSONObject();
        simpleLayout.put("title", title);
        simpleLayout.put("amount", amount);
        simpleLayout.put("date_time", dateTime);
        simpleLayout.put("status", status);
        simpleLayout.put("fields", fields);
        return simpleLayout;
    }

    private void fillWithdrawRecordData(String tenantId, List<IObjectData> data) {
        for (IObjectData datum : data) {
            String paymentStatus = datum.get(WithdrawRecordObjFields.PAYMENT_STATUS, String.class);
            datum.set(WithdrawRecordObjFields.WITHDRAW_QUERY_STATUS, WithdrawStatusQueryEnum.getWithdrawQueryStatus(paymentStatus).code());
        }
    }

    private QueryResult<IObjectData> queryWithdrawRecordsByParam(String tenantId,
                                                                 String withdrawStatus,
                                                                 Long dateTime,
                                                                 RewardPersonInfo rewardPersonInfo,
                                                                 int limit, int offset) {
        SearchTemplateQuery query = new SearchTemplateQuery();

//        query.setSearchSource("db");
        query.setLimit(limit);
        query.setOffset(offset);
        OrderBy order = new OrderBy();
        order.setFieldName(CommonFields.CREATE_TIME);
        order.setIsAsc(false);
        query.setOrders(Lists.newArrayList(order));

        List<IFilter> filters = Lists.newArrayList();

        // 企业人员， 通过 企业 + 员工
        Filter rewardPersonFilter = new Filter();
        rewardPersonFilter.setFieldName(RedPacketRecordObjFields.REWARDED_PERSON_ID);
        rewardPersonFilter.setOperator(Operator.EQ);
        rewardPersonFilter.setFieldValues(Lists.newArrayList(rewardPersonInfo.getRewardPersonId()));
        filters.add(rewardPersonFilter);

        if (rewardPersonInfo.getStoreId() != null) {
            Filter storeFilter = new Filter();
            storeFilter.setFieldName(RedPacketRecordObjFields.ACCOUNT_ID);
            storeFilter.setOperator(Operator.EQ);
            storeFilter.setFieldValues(Lists.newArrayList(rewardPersonInfo.getStoreId()));
            filters.add(storeFilter);
        }

        if (rewardPersonInfo.getCurrentTenantId() != null) {
            // 转入方所在企业 条件
            Filter transfereeTenantFilter = new Filter();
            transfereeTenantFilter.setFieldName(RedPacketRecordObjFields.TRANSFEREE_TENANT_ID);
            transfereeTenantFilter.setOperator(Operator.EQ);
            transfereeTenantFilter.setFieldValues(Lists.newArrayList(rewardPersonInfo.getCurrentTenantId()));
            filters.add(transfereeTenantFilter);
        }

        // 扩展查询条件
        extendWithdrawRecordFilter(withdrawStatus, dateTime, filters);

        query.setFilters(filters);
        return serviceFacade.findBySearchQuery(User.systemUser(tenantId), ApiNames.WITHDRAW_RECORD_OBJ, query);
    }

    private void extendWithdrawRecordFilter(String withdrawStatus, Long dateTime, List<IFilter> filters) {
        if (Objects.nonNull(dateTime)) {
            // 根据入参日期 筛选
            Filter dateTimeFilter = new Filter();
            dateTimeFilter.setFieldName(CommonFields.CREATE_TIME);
            dateTimeFilter.setOperator(Operator.BETWEEN);
            dateTimeFilter.setFieldValues(Lists.newArrayList(getStart(dateTime), getEnd(dateTime)));
            filters.add(dateTimeFilter);
        }

        if (!Strings.isNullOrEmpty(withdrawStatus)) {
            if (WithdrawStatusQueryEnum.WITHDRAW_FAIL.code().equals(withdrawStatus)) {
                Filter withdrawStatusFilter = new Filter();
                withdrawStatusFilter.setFieldName(WithdrawRecordObjFields.PAYMENT_STATUS);
                withdrawStatusFilter.setOperator(Operator.IN);
                withdrawStatusFilter.setFieldValues(Lists.newArrayList(WithdrawPaymentStatusEnum.FAIL.code(), WithdrawPaymentStatusEnum.EXCEPT.code()));
                filters.add(withdrawStatusFilter);
            } else {
                Filter withdrawStatusFilter = new Filter();
                withdrawStatusFilter.setFieldName(WithdrawRecordObjFields.PAYMENT_STATUS);
                withdrawStatusFilter.setOperator(Operator.EQ);
                withdrawStatusFilter.setFieldValues(Lists.newArrayList(WithdrawPaymentStatusEnum.getWithdrawFilterByStatus(withdrawStatus).code()));
                filters.add(withdrawStatusFilter);
            }

        }
    }

    /**
     * 提现预览汇总，校验提现数据
     *
     * @param arg
     * @return
     */
    @Override
    public PreviewWithdraw.Result previewWithdraw(PreviewWithdraw.Arg arg) {
        ApiContext context = ApiContextManager.getContext();
        log.info("context : {}", context);
        // 校验对象  白名单
        List<String> whiteIdList = arg.getWhiteIdList();
        // 黑名单
        List<String> blackIdList = arg.getBlackIdList();
        PreviewWithdraw.Result result = new PreviewWithdraw.Result();
        // 校验登陆人的身份证信息
        validatePersonIdCard(context.getTenantId(), context.getEmployeeId(), context.getOutTenantId(), context.getOutUserId());
        // 当前人可能是企业人员，也可能会是 门店人员
        RewardPersonInfo rewardPersonInfo = getRewardPersonInfo(context.getTenantId(), context.getEmployeeId(), context.getOutTenantId(), context.getOutUserId());

        // 获取蒙牛的 1端企业
        String tenantId = getManufacturerTenantId(context.getTenantId());
        // 统计传入的可提现的红包金额
        BigDecimal amount = summaryRedPacketRecordAmountByTypeWithRecordIds(tenantId,
                RedPacketStatusQueryEnum.AWAIT.code(), rewardPersonInfo, whiteIdList, blackIdList);
        if (amount.compareTo(BigDecimal.ZERO) <= 0) {
            throw new ValidateException(I18N.text(I18NKeys.WITHDRAW_NO_RED_PACKET_AMOUNT_WITHDRAWAL));
        }
        result.setAmount(amount);
        result.setReceivedAccount(I18N.text(I18NKeys.WITHDRAW_ACCOUNT_TYPE_WECHAT_CHANGE));
        return result;
    }

    /**
     * 提现操作，校验提现数据，事物 (异常回滚)
     *
     * @param arg
     * @return
     */
    @Override
    public SubmitWithdraw.Result submitWithdraw(SubmitWithdraw.Arg arg) {
        ApiContext context = ApiContextManager.getContext();
        log.info("context : {}", context);
        BigDecimal beforeAmount = arg.getAmount() == null ? BigDecimal.ZERO : new BigDecimal(arg.getAmount());
        // 校验对象  白名单
        List<String> whiteIdList = arg.getWhiteIdList();
        // 黑名单
        List<String> blackIdList = arg.getBlackIdList();
        SubmitWithdraw.Result result = new SubmitWithdraw.Result();
        // 校验登陆人的身份证信息
        UserIdentityInfo userIdentityInfo = validatePersonIdCard(context.getTenantId(), context.getEmployeeId(), context.getOutTenantId(), context.getOutUserId());
        // 当前人可能是企业人员，也可能会是 门店人员
        RewardPersonInfo rewardPersonInfo = getRewardPersonInfo(context.getTenantId(), context.getEmployeeId(), context.getOutTenantId(), context.getOutUserId());
        // 获取蒙牛的 1端企业
        String tenantId = getManufacturerTenantId(context.getTenantId());
        // try lock
        String lockKey = String.format(PRE_KEY, context.getTenantId(), rewardPersonInfo.getRewardPersonId());
        tryLock(lockKey);
        try {
            List<IObjectData> recordByGroupFieldsData =
                    findRedPacketRecordByGroupFieldsV2(tenantId, RedPacketStatusQueryEnum.AWAIT.code(), rewardPersonInfo, whiteIdList, blackIdList);
            if (CollectionUtils.isEmpty(recordByGroupFieldsData)) {
                throw new ValidateException(I18N.text(I18NKeys.WITHDRAW_NO_RED_PACKET_AMOUNT_WITHDRAWAL));
            }
            // 比较传入的金额与重新计算后的金额是否一致，是否发生改变。
            BigDecimal totalRewardAmount = recordByGroupFieldsData.stream().map(v -> v.get("total_reward_amount_extend", BigDecimal.class))
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            log.info("recordByGroupFieldsData count amount is {}, before amount is {}", totalRewardAmount, beforeAmount);
            if (totalRewardAmount.compareTo(BigDecimal.ZERO) <= 0 ||
                    totalRewardAmount.compareTo(beforeAmount) != 0) {
                throw new ValidateException(I18N.text(I18NKeys.WITHDRAW_RED_PACKET_AMOUNT_CHANGED));
            }
            result.setAmount(totalRewardAmount);
            // 根据 转出方企业ei + 转出方账户类型 + 转出方账户 + 转入方appId + 转入方身份证号 分组，创建提现记录
            createWithdrawByAccountGroup(tenantId, recordByGroupFieldsData, rewardPersonInfo.getRewardPersonId(), userIdentityInfo);
        } finally {
            unlock(lockKey);
        }
        result.setReceivedAccount(I18N.text(I18NKeys.WITHDRAW_ACCOUNT_TYPE_WECHAT_CHANGE));
        return result;
    }

    @Override
    public PersonAuthIdCard.Result personAuthIdCard(PersonAuthIdCard.Arg arg) {
        ApiContext context = ApiContextManager.getContext();
        log.info("context : {}", context);
        String tenantId = context.getTenantId();
        if (!Strings.isNullOrEmpty(context.getOutTenantId()) && !Strings.isNullOrEmpty(context.getOutUserId())) {

            IObjectData publicEmployee = serviceFacade.findObjectData(User.systemUser(tenantId), context.getOutUserId(), ApiNames.PUBLIC_EMPLOYEE_OBJ);
            String contactId = publicEmployee.get(PublicEmployeeFields.CONTRACT_ID, String.class);
            IObjectData contact = serviceFacade.findObjectDataIgnoreAll(User.systemUser(tenantId), contactId, ApiNames.CONTACT_OBJ);
            //是否红包实名认证
            String approve = contact == null ? null : contact.get(ContactFields.MENGNIU_RED_PACKET_APPROVE, String.class);
            return PersonAuthIdCard.Result.builder()
                    .authStatus(OPTION2.equals(approve) ? "1" : "0")
                    .personId(contactId)
                    .apiName(ApiNames.CONTACT_OBJ)
                    .build();
        } else {
            String userId = String.valueOf(context.getEmployeeId());
            IObjectData user = serviceFacade.findObjectDataIgnoreAll(User.systemUser(tenantId), userId, ApiNames.PERSONNEL_OBJ);
            //人员认证状态
            String faceStatus = user.get(PersonnelFields.MENGNIU_FACE_STATUS, String.class);
            //微信认证状态
            String wxStatusNew = user.get(PersonnelFields.MENGNIU_WX_STATUS_NEW, String.class);
            //实施配置的微信认证状态
            String wxStatus = user.get(PersonnelFields.MENGNIU_WX_STATUS, String.class);
            return PersonAuthIdCard.Result.builder()
                    .authStatus(OPTION2.equals(faceStatus) ? "1" : "0")
                    .wxAuthStatus(OPTION2.equals(wxStatus) || OPTION2.equals(wxStatusNew) ? "1" : "0")
                    .personId(userId)
                    .apiName(ApiNames.PERSONNEL_OBJ)
                    .build();
        }
    }

    @Override
    public PersonAlertRecord.Result personAlertRecord(PersonAlertRecord.Arg arg) {
        // 获取当前上下文
        ApiContext context = ApiContextManager.getContext();
        log.info("personAlertRecord context : {}", context);

        String tenantId = context.getTenantId();
        String employeeId = String.valueOf(context.getEmployeeId());

        // 获取蒙牛的 1端企业
        String manufacturerTenantId = getManufacturerTenantId(context.getTenantId());

        // 获取用户标识信息
        AlertRecordInfo alertRecordInfo = new AlertRecordInfo();
        
        // 根据用户类型(内部员工或外部联系人)获取不同信息
        if (!Strings.isNullOrEmpty(context.getOutTenantId()) && !Strings.isNullOrEmpty(context.getOutUserId())) {
            // 外部联系人处理
            IObjectData publicEmployee = serviceFacade.findObjectData(User.systemUser(tenantId), context.getOutUserId(), ApiNames.PUBLIC_EMPLOYEE_OBJ);
            String contactId = publicEmployee.get(PublicEmployeeFields.CONTRACT_ID, String.class);
            String storeId = getStoreIdByOutTenantId(tenantId, context.getOutTenantId(), context.getOutUserId());
            // 获取联系人信息
            IObjectData contact = serviceFacade.findObjectDataIgnoreAll(User.systemUser(tenantId), contactId, ApiNames.CONTACT_OBJ);
            if (contact != null) {
                alertRecordInfo.setRewardPersonId(context.getOutTenantId() + "." + context.getOutUserId());
                alertRecordInfo.setName(contact.get(ContactFields.NAME, String.class));
                alertRecordInfo.setApiName(contact.getDescribeApiName());
                alertRecordInfo.setPhoneNumber(contact.get(ContactFields.MOBILE1, String.class));
                alertRecordInfo.setIdCard(contact.get(ContactFields.MENGNIU_ID_CARD_NUMBER, String.class));
                alertRecordInfo.setAppId(contact.get(ContactFields.MENGNIU_WECHAT_APP_ID, String.class));
                alertRecordInfo.setUnionId(contact.get(ContactFields.MENGNIU_WECHAT_UNION_ID, String.class));
                alertRecordInfo.setOpenId(contact.get(ContactFields.MENGNIU_WECHAT_OPEN_ID, String.class));
                alertRecordInfo.setStoreId(storeId);
            }
        } else {
            // 内部员工处理
            // 获取人员信息
            IObjectData user = serviceFacade.findObjectDataIgnoreAll(User.systemUser(tenantId), employeeId, ApiNames.PERSONNEL_OBJ);
            if (user != null) {
                alertRecordInfo.setRewardPersonId(tenantId + "." + employeeId);
                alertRecordInfo.setName(user.get(PersonnelFields.FULL_NAME, String.class));
                alertRecordInfo.setApiName(user.getDescribeApiName());
                alertRecordInfo.setPhoneNumber(user.get(PersonnelFields.PHONE, String.class));
                alertRecordInfo.setIdCard(user.get(PersonnelFields.MENGNIU_IDCARD, String.class));
                alertRecordInfo.setAppId(user.get(PersonnelFields.MENGNIU_WECHAT_APP_ID, String.class));
                alertRecordInfo.setUnionId(user.get(PersonnelFields.MENGNIU_WX_UNION_ID, String.class));
                alertRecordInfo.setOpenId(user.get(PersonnelFields.MENGNIU_WECHAT_OPEN_ID, String.class));
            }
        }
        // 如果记录不存在，且需要确认，则创建记录
        try {
            IObjectData alertRecord = new ObjectData();
            alertRecord.setDescribeApiName(RedPacketAlertRecordFields.DESCRIBE_API_NAME);
            alertRecord.setTenantId(manufacturerTenantId);
            alertRecord.setOwner(Lists.newArrayList("-10000"));
            alertRecord.setRecordType(CommonFields.RECORD_TYPE__DEFAULT);

            // 使用常量设置记录信息
            alertRecord.set(RedPacketAlertRecordFields.REWARD_PERSON_ID, alertRecordInfo.getRewardPersonId());
            alertRecord.set(RedPacketAlertRecordFields.PERSON_API_NAME, alertRecordInfo.getApiName());
            alertRecord.set(RedPacketAlertRecordFields.PERSON_NAME, alertRecordInfo.getName());
            alertRecord.set(RedPacketAlertRecordFields.PERSON_PHONE, alertRecordInfo.getPhoneNumber());
            alertRecord.set(RedPacketAlertRecordFields.ID_CARD, alertRecordInfo.getIdCard());
            alertRecord.set(RedPacketAlertRecordFields.WX_OPEN_ID, alertRecordInfo.getOpenId());
            alertRecord.set(RedPacketAlertRecordFields.WX_UNION_ID, alertRecordInfo.getUnionId());
            alertRecord.set(RedPacketAlertRecordFields.WX_APP_ID, alertRecordInfo.getAppId());
            alertRecord.set(RedPacketAlertRecordFields.STORE_ID, alertRecordInfo.getStoreId());
            alertRecord.set(RedPacketAlertRecordFields.SIGN_TIME, System.currentTimeMillis());
            alertRecord.set(RedPacketAlertRecordFields.TENANT_ID, tenantId);

            // 保存记录
            serviceFacade.saveObjectData(User.systemUser(manufacturerTenantId), alertRecord);

            // 创建成功
            return PersonAlertRecord.Result.builder()
                    .isConfirm(true)
                    .build();
        } catch (Exception e) {
            log.error("创建红包预警记录失败", e);
            // 创建失败
            throw new ValidateException("签订协议确认失败，系统异常。");
        }
    }

    private void createWithdrawByAccountGroup(String tenantId,
                                              List<IObjectData> recordByGroupFieldsData,
                                              String rewardPersonId,
                                              UserIdentityInfo userIdentityInfo) {
        log.info("recordByGroupFieldsData size is {}", recordByGroupFieldsData.size());
        log.info("userIdentityInfo is {}", userIdentityInfo);
        long nextExecuteTime = System.currentTimeMillis();
        for (IObjectData groupData : recordByGroupFieldsData) {
            BigDecimal withdrawAmount = groupData.get("total_reward_amount_extend", BigDecimal.class);
            List<String> recordIds = CommonUtils.castIgnore(groupData.get("account_record_ids_extend"), String.class);
            List<IObjectData> redPacketRecord = getRedPacketRecordList(tenantId, recordIds);
            transactionProxy.run(() -> {
                // 创建提现记录
                IObjectData withdrawRecord = createWithdrawRecord(tenantId, groupData, withdrawAmount, rewardPersonId, userIdentityInfo);
                // 更新 红包记录
                updateRedPacketStatusWithRewardDetailStatus(tenantId, withdrawRecord.getId(), redPacketRecord);
//                withdrawRecordService.publishWithdraw(withdrawRecord);
                redPacketWithdrawSetter.setUpdateStatusTask(withdrawRecord.getTenantId(), withdrawRecord.getId(), withdrawRecord.getDescribeApiName(), nextExecuteTime);
            });
        }
    }


    private void tryLock(String lockKey) {
        RLock lock = redissonCmd.getLock(lockKey);
        try {
            if (!lock.tryLock(10, 25, TimeUnit.SECONDS)) {
                throw new ValidateException(I18N.text(I18NKeys.WITHDRAW_CURRENTLY_PROGRESS_WITHDRAWAL_OPERATIONS));
            }
        } catch (InterruptedException e) {
            throw new ValidateException(I18N.text(I18NKeys.WITHDRAW_CURRENTLY_PROGRESS_WITHDRAWAL_OPERATIONS));
        }
    }

    private void unlock(String lockKey) {
        RLock lock = redissonCmd.getLock(lockKey);
        if (lock.isLocked() && lock.isHeldByCurrentThread()) {
            lock.unlock();
        }
    }


    private RewardPersonInfo getRewardPersonInfo(String tenantId, Integer employeeId, String outTenantId, String outUserId) {
        String rewardPersonId, storeId = null, storeMasterRole = null;
        if (!Strings.isNullOrEmpty(outTenantId) && !Strings.isNullOrEmpty(outUserId)) {
            rewardPersonId = outTenantId + "." + outUserId;
            // 是互联身份，判断是否是店主身份
            storeMasterRole = validatePersonStoreMasterRole(tenantId, outTenantId, outUserId);
            storeId = getStoreIdByOutTenantId(tenantId, outTenantId, outUserId);
        } else {
            rewardPersonId = tenantId + "." + employeeId;
        }
        RewardPersonInfo rewardPersonInfo = RewardPersonInfo.builder()
                .rewardPersonId(rewardPersonId)
                .storeMasterRole(storeMasterRole)
                .storeId(storeId)
                .currentTenantId(tenantId)
                .build();
        log.info("rewardPersonInfo is {}", rewardPersonInfo);
        return rewardPersonInfo;
    }

    private void updateRedPacketStatusWithRewardDetailStatus(String tenantId,
                                                             String withdrawRecord,
                                                             List<IObjectData> redPacketRecord) {

        List<List<IObjectData>> recordPartition = Lists.partition(redPacketRecord, 200);
        for (List<IObjectData> recordData : recordPartition) {
            // 更新红包提现状态
            Map<String, Object> updateMap = new HashMap<>(4);
            updateMap.put(RedPacketRecordObjFields.WITHDRAWAL_STATUS, RedPacketRecordObjFields.WithdrawalStatus.PROCESSING);
            updateMap.put(RedPacketRecordObjFields.PAYMENT_STATUS, RedPacketRecordObjFields.PaymentStatus.TRANSFERRING);
            updateMap.put(RedPacketRecordObjFields.WITHDRAW_RECORD_ID, withdrawRecord);
            serviceFacade.batchUpdateWithMap(User.systemUser(tenantId), recordData, updateMap);
        }
    }

    private List<IObjectData> getRedPacketRecordList(String tenantId, List<String> recordIds) {
        List<IObjectData> dataList = new ArrayList<>();
        recordIds.forEach(id -> {
            IObjectData objectData = new ObjectData();
            objectData.setTenantId(tenantId);
            objectData.setDescribeApiName(ApiNames.RED_PACKET_RECORD_OBJ);
            objectData.setId(id);
            dataList.add(objectData);
        });
        return dataList;
    }

    private List<IObjectData> findRedPacketRecordByGroupFieldsV2(String tenantId,
                                                                 String type,
                                                                 RewardPersonInfo rewardPersonInfo,
                                                                 List<String> whiteIdList,
                                                                 List<String> blackIdList) {

        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setSearchSource("db");
        query.setLimit(-1);
        query.setOffset(0);
        query.setNeedReturnCountNum(false);

        OrderBy orderBy = new OrderBy();
        orderBy.setFieldName(CommonFields.ID);
        orderBy.setIsAsc(false);
        query.setOrders(Lists.newArrayList(orderBy));

        List<IFilter> recordByTypeFilter = Lists.newArrayList();
        // 人员条件
        buildFilterByPerson(rewardPersonInfo, recordByTypeFilter);
        // 按参数的筛选条件
        buildFilterByRedPacketStatus(type, null, recordByTypeFilter);

        if (CollectionUtils.isNotEmpty(whiteIdList)) {
            Filter recordIdsFilter = new Filter();
            recordIdsFilter.setFieldName(CommonFields.ID);
            recordIdsFilter.setOperator(Operator.IN);
            recordIdsFilter.setFieldValues(Lists.newArrayList(whiteIdList));
            recordByTypeFilter.add(recordIdsFilter);
        }
        if (CollectionUtils.isNotEmpty(blackIdList)) {
            Filter recordIdsFilter = new Filter();
            recordIdsFilter.setFieldName(CommonFields.ID);
            recordIdsFilter.setOperator(Operator.NIN);
            recordIdsFilter.setFieldValues(Lists.newArrayList(blackIdList));
            recordByTypeFilter.add(recordIdsFilter);
        }
        query.setFilters(recordByTypeFilter);
        Map<String, BigDecimal> transfereeAccountByAmountMap = new HashMap<>();
        Map<String, List<String>> recordIdsMap = new HashMap<>();
        List<IObjectData> transfereeAccountByFields = new ArrayList<>();

        QueryDataUtil.findAndConsume(serviceFacade, User.systemUser(tenantId), ApiNames.RED_PACKET_RECORD_OBJ, query, RED_PACKET_FIELDS, dataList -> {
            for (IObjectData objectData : dataList) {
                String key = getTransfereeAccountKey(objectData);
                BigDecimal rewardAmount = objectData.get(RedPacketRecordObjFields.REWARD_AMOUNT, BigDecimal.class);
                if (transfereeAccountByAmountMap.containsKey(key)) {
                    transfereeAccountByAmountMap.put(key, transfereeAccountByAmountMap.getOrDefault(key, BigDecimal.ZERO).add(rewardAmount));
                    recordIdsMap.get(key).add(objectData.getId());
                } else {
                    transfereeAccountByAmountMap.put(key, rewardAmount);
                    recordIdsMap.put(key, Lists.newArrayList(objectData.getId()));
                    transfereeAccountByFields.add(objectData);
                }
            }
        });

        for (IObjectData transfereeAccountByField : transfereeAccountByFields) {
            String key = getTransfereeAccountKey(transfereeAccountByField);
            transfereeAccountByField.set("total_reward_amount_extend", transfereeAccountByAmountMap.getOrDefault(key, BigDecimal.ZERO));
            transfereeAccountByField.set("account_record_ids_extend", recordIdsMap.get(key));
        }
        return transfereeAccountByFields;
    }

    private String getTransfereeAccountKey(IObjectData data) {
        //根据 转出方企业ei + 转出方账户类型 + 转出方账户 + 转入方身份证号 分组，创建提现记录
        String transfereeTenantId = data.get(RedPacketRecordObjFields.TRANSFEROR_TENANT_ID, String.class);
        String transfereeAccountType = data.get(RedPacketRecordObjFields.TRANSFEROR_ACCOUNT_TYPE, String.class);
        String transfereeAccount = data.get(RedPacketRecordObjFields.TRANSFEROR_ACCOUNT, String.class);
        String transfereeId = data.get(RedPacketRecordObjFields.TRANSFEREE_ID, String.class);
        return String.format(PREFIX, transfereeTenantId, transfereeAccountType, transfereeAccount, transfereeId);
    }

    private IObjectData createWithdrawRecord(String tenantId,
                                             IObjectData redPacketRecord,
                                             BigDecimal withdrawAmount,
                                             String rewardPersonId,
                                             UserIdentityInfo userIdentityInfo) {

        // 创建提现记录对象
        IObjectData objectData = buildWithdrawRecordData(redPacketRecord);
        objectData.setDescribeApiName(ApiNames.WITHDRAW_RECORD_OBJ);
        objectData.setOwner(Lists.newArrayList("-10000"));
        objectData.setTenantId(tenantId);
        objectData.setRecordType(CommonFields.RECORD_TYPE__DEFAULT);
        objectData.set(WithdrawRecordObjFields.WITHDRAWAL_AMOUNT, withdrawAmount);
        objectData.set(WithdrawRecordObjFields.WITHDRAWAL_OPERATION_TIME, System.currentTimeMillis());
        objectData.set(WithdrawRecordObjFields.PAYMENT_STATUS, WithdrawPaymentStatusEnum.INIT.code());
        objectData.set(WithdrawRecordObjFields.BUSINESS_ID, IdentityIdGenerator.formWithdrawIdentityId());

        objectData.set(WithdrawRecordObjFields.REWARD_PERSON_ID, rewardPersonId);

        if (Objects.isNull(objectData.get(WithdrawRecordObjFields.TRANSFEREE_ID))) {
            objectData.set(WithdrawRecordObjFields.TRANSFEREE_ID, userIdentityInfo.getIdCard());
        }
        fillUserInfo(userIdentityInfo, objectData);

        return serviceFacade.saveObjectData(User.systemUser(tenantId), objectData);
    }

    private static void fillUserInfo(UserIdentityInfo userIdentityInfo, IObjectData objectData) {
        String idCard = objectData.get(WithdrawRecordObjFields.TRANSFEREE_ID, String.class);
        if (userIdentityInfo.getIdCard() != null && !userIdentityInfo.getIdCard().equals(idCard)){
            log.info("transferee_id is change, before idCard is {}, after idCard is {}", idCard, userIdentityInfo.getIdCard());
            objectData.set(WithdrawRecordObjFields.TRANSFEREE_ID, userIdentityInfo.getIdCard());
        }
        String name = objectData.get(WithdrawRecordObjFields.TRANSFEREE_NAME, String.class);
        if (userIdentityInfo.getName() != null && !userIdentityInfo.getName().equals(name)) {
            log.info("transferee_name is change, before name is {}, after name is {}", name, userIdentityInfo.getName());
            objectData.set(WithdrawRecordObjFields.TRANSFEREE_NAME, userIdentityInfo.getName());
        }
        String wxOpenId = objectData.get(WithdrawRecordObjFields.TRANSFEREE_WECHAT_OPEN_ID, String.class);
        if (userIdentityInfo.getWxOpenId() != null && !userIdentityInfo.getWxOpenId().equals(wxOpenId)) {
            log.info("transferee_wechat_open_id is change, before wxOpenId is {}, after wxOpenId is {}", wxOpenId, userIdentityInfo.getWxOpenId());
            objectData.set(WithdrawRecordObjFields.TRANSFEREE_WECHAT_OPEN_ID, userIdentityInfo.getWxOpenId());

            String wxAppId = objectData.get(WithdrawRecordObjFields.TRANSFEREE_WECHAT_APP_ID, String.class);
            if (userIdentityInfo.getWxAppId() != null && !userIdentityInfo.getWxAppId().equals(wxAppId)) {
                log.info("transferee_wechat_app_id is change, before wxAppId is {}, after wxAppId is {}", wxAppId, userIdentityInfo.getWxAppId());
                objectData.set(WithdrawRecordObjFields.TRANSFEREE_WECHAT_APP_ID, userIdentityInfo.getWxAppId());
            }
        }
    }

    private IObjectData buildWithdrawRecordData(IObjectData objectData) {
        IObjectData withdrawRecordData = new ObjectData();
        // 转入方
        withdrawRecordData.set(WithdrawRecordObjFields.TRANSFEREE_ID, objectData.get(RedPacketRecordObjFields.TRANSFEREE_ID, String.class));
        withdrawRecordData.set(WithdrawRecordObjFields.TRANSFEREE_PHONE, objectData.get(RedPacketRecordObjFields.TRANSFEREE_PHONE, String.class));
        withdrawRecordData.set(WithdrawRecordObjFields.TRANSFEREE_ACCOUNT_TYPE, objectData.get(RedPacketRecordObjFields.TRANSFEREE_ACCOUNT_TYPE, String.class));
        withdrawRecordData.set(WithdrawRecordObjFields.TRANSFEREE_WECHAT_OPEN_ID, objectData.get(RedPacketRecordObjFields.TRANSFEREE_WECHAT_OPEN_ID, String.class));
        withdrawRecordData.set(WithdrawRecordObjFields.TRANSFEREE_WECHAT_APP_ID, objectData.get(RedPacketRecordObjFields.TRANSFEREE_WECHAT_APP_ID, String.class));
        withdrawRecordData.set(WithdrawRecordObjFields.TRANSFEREE_NAME, objectData.get(RedPacketRecordObjFields.TRANSFEREE_NAME, String.class));
        // 转出方
        withdrawRecordData.set(WithdrawRecordObjFields.TRANSFEROR_ACCOUNT_TYPE, objectData.get(RedPacketRecordObjFields.TRANSFEROR_ACCOUNT_TYPE, String.class));
        withdrawRecordData.set(WithdrawRecordObjFields.TRANSFEROR_NAME, objectData.get(RedPacketRecordObjFields.TRANSFEROR_NAME, String.class));
        withdrawRecordData.set(WithdrawRecordObjFields.TRANSFEROR_ID, objectData.get(RedPacketRecordObjFields.TRANSFEROR_ID, String.class));
        withdrawRecordData.set(WithdrawRecordObjFields.TRANSFEROR_WECHAT_OPEN_ID, objectData.get(RedPacketRecordObjFields.TRANSFEROR_WECHAT_OPEN_ID, String.class));
        withdrawRecordData.set(WithdrawRecordObjFields.TRANSFEROR_PHONE, objectData.get(RedPacketRecordObjFields.TRANSFEROR_PHONE, String.class));
        withdrawRecordData.set(WithdrawRecordObjFields.TRANSFEROR_ACCOUNT, objectData.get(RedPacketRecordObjFields.TRANSFEROR_ACCOUNT, String.class));
        withdrawRecordData.set(WithdrawRecordObjFields.TRANSFEROR_TENANT_ID, objectData.get(RedPacketRecordObjFields.TRANSFEROR_TENANT_ID, String.class));

        return withdrawRecordData;
    }

    private void buildFilterByPerson(RewardPersonInfo rewardPersonInfo,
                                     List<IFilter> filters) {
        // 1 : 店老板
        if (IS_STORE_MASTER.equals(rewardPersonInfo.getStoreMasterRole())) {
            // 门店老板 ，通过店主角色 条件
            Filter storeMasterRoleFilter = new Filter();
            storeMasterRoleFilter.setFieldName(RedPacketRecordObjFields.REWARD_PART_CODE);
            storeMasterRoleFilter.setOperator(Operator.EQ);
            storeMasterRoleFilter.setFieldValues(Lists.newArrayList(rewardPersonInfo.getStoreMasterRole()));
            filters.add(storeMasterRoleFilter);
        } else {
            // 企业人员， 通过 企业 + 员工
            Filter rewardPersonFilter = new Filter();
            rewardPersonFilter.setFieldName(RedPacketRecordObjFields.REWARDED_PERSON_ID);
            rewardPersonFilter.setOperator(Operator.EQ);
            rewardPersonFilter.setFieldValues(Lists.newArrayList(rewardPersonInfo.getRewardPersonId()));
            filters.add(rewardPersonFilter);

            // 非门店老板角色
            Filter storeMasterRoleFilter = new Filter();
            storeMasterRoleFilter.setFieldName(RedPacketRecordObjFields.REWARD_PART_CODE);
            storeMasterRoleFilter.setOperator(Operator.N);
            storeMasterRoleFilter.setFieldValues(Lists.newArrayList(IS_STORE_MASTER));
            filters.add(storeMasterRoleFilter);
        }

        if (rewardPersonInfo.getStoreId() != null) {
            Filter storeFilter = new Filter();
            storeFilter.setFieldName(RedPacketRecordObjFields.ACCOUNT_ID);
            storeFilter.setOperator(Operator.EQ);
            storeFilter.setFieldValues(Lists.newArrayList(rewardPersonInfo.getStoreId()));
            filters.add(storeFilter);
        }

        if (rewardPersonInfo.getCurrentTenantId() != null) {
            // 转入方所在企业 条件
            Filter transfereeTenantFilter = new Filter();
            transfereeTenantFilter.setFieldName(RedPacketRecordObjFields.TRANSFEREE_TENANT_ID);
            transfereeTenantFilter.setOperator(Operator.EQ);
            transfereeTenantFilter.setFieldValues(Lists.newArrayList(rewardPersonInfo.getCurrentTenantId()));
            filters.add(transfereeTenantFilter);
        }
    }

}
