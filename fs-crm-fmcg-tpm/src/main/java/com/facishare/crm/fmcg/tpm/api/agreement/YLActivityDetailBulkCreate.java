package com.facishare.crm.fmcg.tpm.api.agreement;

import com.facishare.paas.metadata.api.IObjectData;
import lombok.Builder;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

public interface YLActivityDetailBulkCreate {
    @Data
    @ToString
    class Arg implements Serializable {

        private List<Map<String,Object>> args;
    }


    @Data
    @Builder
    @ToString
    class Result implements Serializable {


        private List<IObjectData> data;
    }
}
