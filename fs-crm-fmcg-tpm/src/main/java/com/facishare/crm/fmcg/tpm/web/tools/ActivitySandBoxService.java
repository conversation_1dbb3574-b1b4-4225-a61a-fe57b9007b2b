package com.facishare.crm.fmcg.tpm.web.tools;

import com.alibaba.fastjson.JSON;
import com.facishare.converter.impl.EIEAConverterImpl;
import com.facishare.crm.fmcg.tpm.dao.mongo.ConfigDAO;
import com.facishare.crm.fmcg.tpm.dao.mongo.ActivityNodeTemplateDAO;
import com.facishare.crm.fmcg.tpm.dao.mongo.ActivityTypeDAO;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.*;
import com.facishare.crm.fmcg.common.http.ApiContext;
import com.facishare.crm.fmcg.common.http.ApiContextManager;
import com.facishare.crm.fmcg.tpm.service.abstraction.PluginInstanceService;
import com.facishare.crm.fmcg.tpm.web.contract.ActivitySandBox;
import com.facishare.crm.fmcg.tpm.web.contract.AddConfig;
import com.facishare.crm.fmcg.tpm.web.contract.model.ConfigVO;
import com.facishare.crm.fmcg.tpm.web.manager.abstraction.IActivityTypeManager;
import com.facishare.crm.fmcg.tpm.web.service.abstraction.BaseService;
import com.facishare.crm.fmcg.tpm.web.tools.abstraction.IActivitySandBoxService;
import com.facishare.crm.fmcg.tpm.web.service.abstraction.IConfigService;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * author: wuyx
 * description:
 * createTime: 2022/3/17 10:31
 */
@Service
@Slf4j
public class ActivitySandBoxService extends BaseService implements IActivitySandBoxService {

    @Resource
    protected EIEAConverterImpl eieaConverterImpl;
    @Resource
    private ActivityTypeDAO activityTypeDAO;
    @Resource
    private ActivityNodeTemplateDAO activityNodeTemplateDAO;
    @Resource
    private IActivityTypeManager activityTypeManager;
    @Resource
    private ConfigDAO configDAO;
    @Resource
    private IConfigService tpmConfigService;
    @Resource
    private PluginInstanceService pluginService;

    private static final int COUNT = 200;

    @Override
    public ActivitySandBox.Result sandBoxCopy(ActivitySandBox.Arg arg) {
        log.info("init sandBox arg={}", JSON.toJSONString(arg));

        String sourceEa = arg.getSourceEA();
        String targetEa = arg.getTargetEA();
        int sourceEi = eieaConverterImpl.enterpriseAccountToId(sourceEa);
        int targetEi = eieaConverterImpl.enterpriseAccountToId(targetEa);

        //活动节点
        log.info("sandBox start copyActivityNode");
        Map<String, String> activityNodeCache = Maps.newHashMap();
        copyActivityNode(sourceEi, targetEi, activityNodeCache);

        // 活动类型
        log.info("sandBox start copyActivity, activityNodeCache={}", JSON.toJSONString(activityNodeCache));
        copyActivity(sourceEi, targetEi, activityNodeCache);

        //刷新活动方案中的活动类型
        activityTypeManager.publishSyncActivityTypeFieldTask(String.valueOf(targetEi));

        //客户类型设置
        copyConfig(sourceEi, targetEi);

        return ActivitySandBox.Result.builder().code(0).errorMessage("").build();
    }

    @Override
    public void copyConfig(int sourceEi, int targetEi) {
        List<ConfigPO> sourceSets = configDAO.queryAll(String.valueOf(sourceEi));
        if (CollectionUtils.isNotEmpty(sourceSets)) {
            List<ConfigVO> targetSets = Lists.newArrayList();
            sourceSets.forEach(po -> {
                ConfigVO targetVo = new ConfigVO();
                targetVo.setKey(po.getKey());
                targetVo.setValue(po.getValue());
                targetSets.add(targetVo);
            });

            doSaveConfig(String.valueOf(targetEi), targetSets);
        }
    }

    private void doSaveConfig(String tenantId, List<ConfigVO> vos) {
        ApiContextManager.setContext(ApiContext.builder()
                .tenantId(tenantId)
                .tenantAccount(tenantId)
                .employeeId(-10000)
                .appId("FMCG_TPM")
                .build());

        AddConfig.Arg arg = new AddConfig.Arg();
        arg.setConfigs(vos);
        tpmConfigService.save(arg);
    }

    private void copyActivity(int sourceEi, int targetEi, Map<String, String> activityNodeCache) {
        int limit = 100;
        int offset = 0;

        int targetSum = 0;
        List<String> activityTypeTargetNames = Lists.newArrayList();
        while (true) {
            if (targetSum > COUNT) {
                break;
            }
            List<ActivityTypePO> targetData = activityTypeDAO.allListByTenantId(limit, offset, String.valueOf(targetEi));
            if (CollectionUtils.isNotEmpty(targetData)) {
                activityTypeTargetNames.addAll(targetData.stream().map(ActivityTypePO::getName).collect(Collectors.toList()));
            }

            if (targetData.size() < limit) {
                break;
            }
            offset += limit;
            targetSum++;
        }

        log.info("sandBox type targetNames:{} ,size:{}", JSON.toJSONString(activityTypeTargetNames), activityTypeTargetNames.size());

        int copySum = 0;
        while (true) {
            if (copySum > COUNT) {
                break;
            }
            List<ActivityTypePO> data = activityTypeDAO.allListByTenantId(limit, offset, String.valueOf(sourceEi));
            for (ActivityTypePO activityTypePo : data) {
                if (CollectionUtils.isNotEmpty(activityTypeTargetNames) && activityTypeTargetNames.contains(activityTypePo.getName())) {
                    continue;
                }
                activityTypePo.setId(null);
                for (ActivityNodeEntity nodeEntity : activityTypePo.getActivityNodes()) {
                    String templateId = nodeEntity.getTemplateId();
                    if (activityNodeCache.containsKey(templateId)) {
                        nodeEntity.setTemplateId(activityNodeCache.getOrDefault(templateId, ""));

                        if (nodeEntity.getType().equals(NodeType.AUDIT.value())
                                && !Strings.isNullOrEmpty(nodeEntity.getActivityProofAuditConfig().getAuditSourceConfig().getTemplateId())) {
                            String auditTemplateId = nodeEntity.getActivityProofAuditConfig().getAuditSourceConfig().getTemplateId();
                            nodeEntity.getActivityProofAuditConfig().getAuditSourceConfig().setTemplateId(activityNodeCache.getOrDefault(auditTemplateId, ""));
                        }
                        if (nodeEntity.getType().equals(NodeType.WRITE_OFF.value())
                                && !Strings.isNullOrEmpty(nodeEntity.getActivityWriteOffConfig().getWriteOffSourceConfig().getTemplateId())) {
                            String writeOffTemplateId = nodeEntity.getActivityWriteOffConfig().getWriteOffSourceConfig().getTemplateId();
                            nodeEntity.getActivityWriteOffConfig().getWriteOffSourceConfig().setTemplateId(activityNodeCache.getOrDefault(writeOffTemplateId, ""));
                        }
                        if (nodeEntity.getType().equals(NodeType.STORE_WRITE_OFF.value())){
                            String apiName = nodeEntity.getActivityStoreWriteOffConfig().getStoreWriteOffSourceConfig().getApiName();
                            //如果复制的节点包括门店费用核销，生成插件。
                            createStoreWriteOffPlugin(targetEi, apiName);
                        }

                    }
                }
                activityTypeDAO.add(String.valueOf(targetEi), -10000, activityTypePo);
            }
            if (data.size() < limit) {
                break;
            }
            offset += limit;
            copySum++;
        }
    }

    private void createStoreWriteOffPlugin(int targetEi, String apiName) {
        //查询对象是否已绑定插件, 否，则add
        if (!pluginService.findPluginUnit(String.valueOf(targetEi), apiName, "tpm_store_write_off")) {
            try {
                pluginService.addPluginUnit(targetEi, -10000, apiName, "tpm_store_write_off");
            } catch (Exception e) {
                log.error("add storeWriteOff plugin fail, e:", e);
            }
        }
    }

    private void copyActivityNode(int sourceEi, int targetEi, Map<String, String> activityNodeCache) {
        int limit = 100;
        int offset = 0;

        Map<String, String> targetObjectApiNames = Maps.newHashMap();
        int targetSum = 0;
        while (true) {
            if (targetSum > COUNT) {
                break;
            }
            List<ActivityNodeTemplatePO> targetData = activityNodeTemplateDAO.allListByTenantId(String.valueOf(targetEi), offset, limit);
            if (CollectionUtils.isNotEmpty(targetData)) {
                targetObjectApiNames.putAll(targetData.stream().collect(Collectors.toMap(ActivityNodeTemplatePO::getObjectApiName, v -> v.getId().toHexString())));
            }
            if (targetData.size() < limit) {
                break;
            }
            offset += limit;
            targetSum++;
        }

        log.info("sandBox node targetObjectApiNames:{} ,size:{}", JSON.toJSONString(targetObjectApiNames), targetObjectApiNames.size());

        int copySum = 0;
        while (true) {
            if (copySum > COUNT) {
                break;
            }
            List<ActivityNodeTemplatePO> data = activityNodeTemplateDAO.allListByTenantId(String.valueOf(sourceEi), offset, limit);
            for (ActivityNodeTemplatePO activityNodeTemplatePo : data) {
                String oldActivityNodeId = activityNodeTemplatePo.getId().toString();
                if (!targetObjectApiNames.isEmpty() && targetObjectApiNames.containsKey(activityNodeTemplatePo.getObjectApiName())) {
                    activityNodeCache.put(oldActivityNodeId, targetObjectApiNames.get(activityNodeTemplatePo.getObjectApiName()));
                    continue;
                }
                activityNodeTemplatePo.setId(null);
                String activityNodeId = activityNodeTemplateDAO.add(String.valueOf(targetEi), -10000, activityNodeTemplatePo);
                activityNodeCache.put(oldActivityNodeId, activityNodeId);
            }
            if (data.size() < limit) {
                break;
            }
            offset += limit;
            copySum++;
        }
    }
}
