package com.facishare.crm.fmcg.tpm.service;

import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.common.apiname.RedPacketRecordObjFields;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/5 17:27
 */
@Slf4j
@Service
public class RedPacketRecordService {

    @Resource
    private ServiceFacade serviceFacade;

    public void batchUpdateRedPacketStatusById(String tenantId, String redPacketId) {
        User user = User.systemUser(tenantId);
        IObjectData redPacketData = serviceFacade.findObjectData(user, redPacketId, ApiNames.RED_PACKET_RECORD_OBJ);
        Long expirationTime = redPacketData.get(RedPacketRecordObjFields.EXPIRATION_TIME, Long.class, Long.MAX_VALUE);
        String redPacketStatus = redPacketData.get(RedPacketRecordObjFields.RED_PACKET_STATUS, String.class);
        String withdrawalStatus = redPacketData.get(RedPacketRecordObjFields.WITHDRAWAL_STATUS, String.class);
        // 过期时间小于当前时间,且红包状态为生效中， 红包状态更新为过期。
        if (System.currentTimeMillis() > expirationTime
                && RedPacketRecordObjFields.RedPacketStatus.EFFECTUATE.equals(redPacketStatus)
                && RedPacketRecordObjFields.WithdrawalStatus.AWAIT.equals(withdrawalStatus)) {
            batchUpdateRedPacketStatus(user, Lists.newArrayList(redPacketData));
        }
    }

    public void batchUpdateRedPacketStatus(User user, List<IObjectData> redPacketDataList) {
        for (IObjectData redPacketData : redPacketDataList) {
            redPacketData.set(RedPacketRecordObjFields.RED_PACKET_STATUS, RedPacketRecordObjFields.RedPacketStatus.EXPIRED);
        }
        List<String> updateFields = Lists.newArrayList(RedPacketRecordObjFields.RED_PACKET_STATUS);
        if (redPacketDataList.size() < 100) {
            serviceFacade.batchUpdateByFields(user, redPacketDataList, updateFields);
        } else {
            for (List<IObjectData> redPacketData : Lists.partition(redPacketDataList, 100)) {
                serviceFacade.batchUpdateByFields(user, redPacketData, updateFields);
            }
        }
    }
}
