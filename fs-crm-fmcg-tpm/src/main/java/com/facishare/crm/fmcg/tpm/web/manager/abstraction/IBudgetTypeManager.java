package com.facishare.crm.fmcg.tpm.web.manager.abstraction;

import com.facishare.crm.fmcg.tpm.dao.mongo.po.BudgetTypeNodeEntity;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.BudgetTypePO;
import com.facishare.crm.fmcg.tpm.web.contract.model.BudgetTypeNodeVO;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface IBudgetTypeManager {

    void publishSyncBudgetTypeFieldTask(String tenantId);

    void publishSyncBudgetNodeFieldTask(String tenantId);

    boolean isUsed(String tenantId, String id);

    List<BudgetTypeNodeEntity> convertToNodesWhenUnused(String tenantId, BudgetTypeNodeVO root);

    List<BudgetTypeNodeEntity> convertToNodesWhenUsed(BudgetTypeNodeVO root, List<BudgetTypeNodeEntity> nodes);

    BudgetTypeNodeVO convertToRootNodeVO(List<BudgetTypeNodeEntity> nodes);

    BudgetTypePO get(String tenantId, String typeId);

    void delete(String tenantId, int employeeId, String typeId);

    void deleteObjectDataByTypeId(String tenantId, String typeId);

    BudgetTypeNodeEntity getNode(String tenantId, String typeId, String nodeId);

    List<BudgetTypePO> query(String tenantId, List<String> typeIds);

    void basicInformationValidation(String tenantId, BudgetTypePO po);

    void editDepartmentRangeValidate(String tenantId, List<Integer> newRange, List<Integer> oldRange);

    int maximumDepartmentLevel(String tenantId, List<Integer> departmentRange);

    void publishSyncCreateBudgetTypeReference(String tenantId, BudgetTypePO po);

    void publishSyncEditBudgetTypeReference(String tenantId, BudgetTypePO oldBudgetType, BudgetTypePO newBudgetType);

    void publishSyncDeleteBudgetTypeReference(String tenantId, BudgetTypePO po);
}
