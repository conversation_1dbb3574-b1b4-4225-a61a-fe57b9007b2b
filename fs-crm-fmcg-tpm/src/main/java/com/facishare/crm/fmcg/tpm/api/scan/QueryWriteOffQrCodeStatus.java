package com.facishare.crm.fmcg.tpm.api.scan;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;

public interface QueryWriteOffQrCodeStatus {


    @Data
    @ToString
    class Arg implements Serializable {

        @JSONField(name = "record_token")
        @JsonProperty(value = "record_token")
        @SerializedName("record_token")
        private String recordToken;

        private String environment;
    }


    @Data
    @ToString
    @NoArgsConstructor
    @AllArgsConstructor
    class Result implements Serializable {

        private Boolean used;
    }
}
