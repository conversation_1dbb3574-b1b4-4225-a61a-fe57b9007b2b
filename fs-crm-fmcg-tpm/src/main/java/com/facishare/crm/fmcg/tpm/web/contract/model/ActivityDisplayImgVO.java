package com.facishare.crm.fmcg.tpm.web.contract.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityDisplayImgPO;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

/**
 * 展示图片VO对象
 * 
 * 活动展示图片值对象
 */
@Data
@ToString
@SuppressWarnings("Duplicates")
public class ActivityDisplayImgVO implements Serializable {

    private String id;
    
    @JSONField(name = "n_path")
    @JsonProperty(value = "n_path")
    @SerializedName("n_path")
    private String nPath;
    
    @JSONField(name = "proof_id")
    @JsonProperty(value = "proof_id")
    @SerializedName("proof_id")
    private String proofId;
    
    @JSONField(name = "visit_id")
    @JsonProperty(value = "visit_id")
    @SerializedName("visit_id")
    private String visitId;
    
    @JSONField(name = "activity_display_img_id")
    @JsonProperty(value = "activity_display_img_id")
    @SerializedName("activity_display_img_id")
    private String activityDisplayImgId;
    
    private String status;
    
    @JSONField(name = "tenant_id")
    @JsonProperty(value = "tenant_id")
    @SerializedName("tenant_id")
    private String tenantId;
    
    @JSONField(name = "creator")
    @JsonProperty(value = "creator")
    @SerializedName("creator")
    private Integer creator;
    
    @JSONField(name = "create_time")
    @JsonProperty(value = "create_time")
    @SerializedName("create_time")
    private Long createTime;
    
    @JSONField(name = "last_updater")
    @JsonProperty(value = "last_updater")
    @SerializedName("last_updater")
    private Integer lastUpdater;
    
    @JSONField(name = "last_update_time")
    @JsonProperty(value = "last_update_time")
    @SerializedName("last_update_time")
    private Long lastUpdateTime;
    
    @JSONField(name = "deleted")
    @JsonProperty(value = "deleted")
    @SerializedName("deleted")
    private Boolean deleted;
    
    @JSONField(name = "delete_by")
    @JsonProperty(value = "delete_by")
    @SerializedName("delete_by")
    private Integer deleteBy;
    
    @JSONField(name = "delete_time")
    @JsonProperty(value = "delete_time")
    @SerializedName("delete_time")
    private Long deleteTime;
    
    @JSONField(name = "error_message")
    @JsonProperty(value = "error_message")
    @SerializedName("error_message")
    private String errorMessage;

    public static ActivityDisplayImgVO fromPO(ActivityDisplayImgPO po) {
        if (po == null) {
            return null;
        }
        ActivityDisplayImgVO vo = new ActivityDisplayImgVO();
        vo.setId(po.getId().toString());
        vo.setNPath(po.getNPath());
        vo.setProofId(po.getProofId());
        vo.setVisitId(po.getVisitId());
        vo.setActivityDisplayImgId(po.getActivityDisplayImgId());
        vo.setStatus(po.getStatus());
        vo.setTenantId(po.getTenantId());
        vo.setCreator(po.getCreator());
        vo.setCreateTime(po.getCreateTime());
        vo.setLastUpdater(po.getLastUpdater());
        vo.setLastUpdateTime(po.getLastUpdateTime());
        vo.setDeleted(po.isDeleted());
        vo.setDeleteBy(po.getDeleteBy());
        vo.setDeleteTime(po.getDeleteTime());
        vo.setErrorMessage(po.getErrorMessage());
        return vo;
    }
} 