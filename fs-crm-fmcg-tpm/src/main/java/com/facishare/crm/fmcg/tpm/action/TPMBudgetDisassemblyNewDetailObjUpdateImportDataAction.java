package com.facishare.crm.fmcg.tpm.action;

import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.predef.action.StandardUpdateImportDataAction;
import lombok.extern.slf4j.Slf4j;

/**
 * ligt
 */
@Slf4j
public class TPMBudgetDisassemblyNewDetailObjUpdateImportDataAction extends StandardUpdateImportDataAction {

    @Override
    protected void before(Arg arg) {
        throw new ValidateException("update import not allowed!");
    }

}
