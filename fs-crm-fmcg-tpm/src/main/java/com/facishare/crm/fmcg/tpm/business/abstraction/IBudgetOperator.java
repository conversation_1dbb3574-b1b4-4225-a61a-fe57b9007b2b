package com.facishare.crm.fmcg.tpm.business.abstraction;

import com.facishare.crm.fmcg.tpm.business.enums.BizType;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.BudgetTypeNodeEntity;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;

import java.math.BigDecimal;
import java.util.Map;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2022/8/3 11:17
 */
public interface IBudgetOperator {

    void init(
            BizType biz,
            User user,
            String id,
            String businessTraceId,
            String approvalTraceId,
            IObjectData relatedObjectData
    );

    void init(
            BizType biz,
            User user,
            String id,
            String businessTraceId,
            String approvalTraceId,
            IObjectData relatedObjectData,
            boolean isValidateBudgetEnable,
            boolean isValidateWhat
    );

    void init(
            BizType biz,
            User user,
            String id,
            String businessTraceId,
            String approvalTraceId
    );

    /**
     * 设置关联对象信息，一般在 operator 初始化时对象数据还没生成时使用
     *
     * @param what 关联对象信息
     */
    void setWhat(IObjectData what);

    /**
     * 获取相关预算表
     *
     * @return 预算表信息
     */
    IObjectData getAccount();

    BudgetTypeNodeEntity getNode();


    String getBusinessTraceId();


    String getApprovalTraceId();


    IObjectData getWhat();


    /**
     * 锁定预算表，可重入
     *
     * @return 成功与否
     */
    boolean tryLock();

    /**
     * 锁定预算表，可重入
     *
     * @return 成功与否
     */
    boolean tryLock(long wait);

    /**
     * 解锁
     */
    void unlock();

    /**
     * 校验可操作金额，例如：拆解，调拨等
     *
     * @param amount 需要操作的金额
     */
    void validateOperableAmount(BigDecimal amount);

    /**
     * 获取可操作的金额
     *
     * @return 可操作金额
     */
    BigDecimal operableAmount();

    /**
     * 校验可消费金额，例如：消费规则消费，自定义消费
     *
     * @param amount 需要消费的金额
     */
    void validateConsumableAmount(BigDecimal amount);

    /**
     * 校验可消费金额，例如：消费规则消费，自定义消费
     *
     * @param batchConsumeAmount 需要消费的金额
     */
    void validateConsumableAmount(Map<String, BigDecimal> batchConsumeAmount);

    /**
     * 获取可消费金额
     *
     * @return 可消费金额
     */
    BigDecimal consumableAmount();

    void validateWriteOffAmount(BigDecimal amount);

    void validateOverLimitWriteOffAmount(BigDecimal amount);

    BigDecimal frozenAmount();

    /**
     * 获取显示的金额信息
     *
     * @return 金额信息
     */
    Map<String, BigDecimal> realAmount();

    /**
     * 重算金额信息
     */
    void recalculate();

    /**
     * 冻结
     *
     * @param amount 冻结金额
     */
    IObjectData freeze(BigDecimal amount);

    /**
     * 解冻
     *
     * @param unfreezeBizType 解冻类型
     * @return 解冻金额
     */
    BigDecimal unfreeze(BizType unfreezeBizType);

    /**
     * 解冻
     *
     * @param unfreezeBizType 解冻类型
     * @return 部分解冻
     */
    BigDecimal unfreeze(BizType unfreezeBizType, BigDecimal amount);


    /**
     * 解冻
     *
     * @param unfreezeBizType 解冻类型
     * @return 部分解冻
     */
    IObjectData unfreeze(BigDecimal amount, BizType unfreezeBizType);

    /**
     * 扣减
     *
     * @param amount 扣减金额
     */
    IObjectData expenditure(BigDecimal amount);

    /**
     * 增加
     *
     * @param amount 增加金额
     */
    IObjectData income(BigDecimal amount);

    /**
     * 冻结剩余金额
     */
    void freezeRemainingAmount();

    /**
     * 占用
     *
     * @param amount 占用金额
     */
    IObjectData occupy(BigDecimal amount);

    /**
     * 释放占用
     */
    void releaseOccupy();

    User getUser();
}
