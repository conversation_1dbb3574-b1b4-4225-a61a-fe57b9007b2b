package com.facishare.crm.fmcg.tpm.web.poc;


import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.common.apiname.TPMStoreWriteOffFields;
import com.facishare.crm.fmcg.tpm.business.abstraction.IPOCTriggerActionService;
import com.facishare.crm.fmcg.common.http.ApiContext;
import com.facishare.crm.fmcg.common.http.ApiContextManager;
import com.facishare.crm.fmcg.tpm.web.contract.TriggerAction;
import com.facishare.crm.fmcg.tpm.web.contract.poc.POCStoreWriteOff;
import com.facishare.crm.fmcg.tpm.web.poc.abstraction.AbstractPOCHandler;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.BaseObjectSaveAction;
import com.facishare.paas.metadata.api.IObjectData;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
@SuppressWarnings({"Duplicates"})
public class StoreWriteOffPOCHandler extends AbstractPOCHandler<POCStoreWriteOff.Arg, POCStoreWriteOff.Result> {


    @Resource
    private IPOCTriggerActionService pocTriggerActionService;

    @Override
    protected void before(POCStoreWriteOff.Arg arg) {

        IObjectData objectData = initStoreWriteOff(arg);


        arg.setMasterObj(objectData);
        super.before(arg);
    }


    @Override
    protected POCStoreWriteOff.Result doAct(POCStoreWriteOff.Arg arg, POCStoreWriteOff.Result result) {
        ApiContext context = ApiContextManager.getContext();


        BaseObjectSaveAction.Result createResult = pocTriggerActionService.triggerAction(TriggerAction.Arg.builder().user(User.systemUser(context.getTenantId())).objectData(arg.getMasterObj()).details(arg.getDetails()).apiName(ApiNames.TPM_STORE_WRITE_OFF_OBJ)
                .detailApiName(ApiNames.TPM_STORE_WRITE_OFF_CASHING_PRODUCT_OBJ).actionName("Edit").triggerFlow(false).triggerFlow(false).build());
        result.setMasterObj(createResult.getObjectData().toObjectData());
        return result;
    }

    @Override
    protected POCStoreWriteOff.Result after(POCStoreWriteOff.Arg arg, POCStoreWriteOff.Result result) {

        return super.after(arg, result);
    }

    private IObjectData initStoreWriteOff(POCStoreWriteOff.Arg arg) {
        IObjectData masterObj = arg.getMasterObj();
        masterObj.set(TPMStoreWriteOffFields.STORE_STANDARD, true);
        masterObj.set(TPMStoreWriteOffFields.CONFIRMED_AMOUNT, masterObj.get(TPMStoreWriteOffFields.AUDITED_AMOUNT));
        return masterObj;
    }


}
