package com.facishare.crm.fmcg.tpm.web.contract;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.metadata.api.IObjectData;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/6/28 下午7:26
 */
public interface StoreWriteOffDataList {

    @Data
    @ToString
    class Arg implements Serializable {

        // 门店费用核销 id
        @SerializedName("source_id")
        @JSONField(name = "source_id")
        @JsonProperty("source_id")
        private String sourceId;

        @SerializedName("field")
        @JSONField(name = "field")
        @JsonProperty("field")
        private String field;

        @SerializedName("api_name")
        @JSONField(name = "api_name")
        @JsonProperty("api_name")
        private String apiName;
    }

    @Data
    @ToString
    class Result implements Serializable {

        @SerializedName("object_data")
        @JSONField(name = "object_data")
        @JsonProperty("object_data")
        private List<IObjectData> objectData;
    }

}
