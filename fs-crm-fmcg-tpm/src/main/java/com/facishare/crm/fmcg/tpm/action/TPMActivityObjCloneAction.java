package com.facishare.crm.fmcg.tpm.action;

import com.facishare.crm.fmcg.common.apiname.TPMActivityFields;
import com.facishare.crm.fmcg.tpm.service.abstraction.TransactionService;
import com.facishare.crm.fmcg.tpm.utils.TimeUtils;
import com.facishare.paas.appframework.core.predef.action.StandardCloneAction;
import com.facishare.paas.metadata.api.IObjectData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class TPMActivityObjCloneAction extends StandardCloneAction implements TransactionService<StandardCloneAction.Arg, StandardCloneAction.Result> {

    private static final Logger LOGGER = LoggerFactory.getLogger(TPMActivityObjCloneAction.class);

    @Override
    public Result doActTransaction(Arg arg) {
        return super.doAct(arg);
    }

    @Override
    protected Result after(Arg arg, Result result) {
        Result localResult = super.after(arg, result);
        IObjectData objectData = localResult.getObjectData().toObjectData();
        Long beginDate = objectData.get(TPMActivityFields.BEGIN_DATE, Long.class);
        Long endDate = objectData.get(TPMActivityFields.END_DATE, Long.class);
        if (beginDate != null && beginDate <= TimeUtils.MIN_DATE) {
            objectData.set(TPMActivityFields.BEGIN_DATE, null);
        }
        if (endDate != null && endDate >= TimeUtils.MAX_DATE) {
            objectData.set(TPMActivityFields.END_DATE, null);
        }
        objectData.set(TPMActivityFields.ACTIVITY_ACTUAL_AMOUNT, "0");
        LOGGER.info("clone activity begin data is {} end data is {}", beginDate, endDate);
        return localResult;
    }
}
