package com.facishare.crm.fmcg.tpm.web.contract.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityProofAuditModeConfigEntity;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2021/12/6 19:42
 */
@Data
@ToString
public class ActivityProofAuditModeConfigVO implements Serializable {

    @JSONField(name = "audit_mode")
    @JsonProperty(value = "audit_mode")
    @SerializedName("audit_mode")
    private String auditMode;


    public static ActivityProofAuditModeConfigVO fromPO(ActivityProofAuditModeConfigEntity po) {
        if (po == null) {
            return null;
        }
        ActivityProofAuditModeConfigVO vo = new ActivityProofAuditModeConfigVO();
        vo.setAuditMode(po.getAuditMode());
        return vo;
    }
}
