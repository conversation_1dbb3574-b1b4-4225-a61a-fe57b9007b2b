package com.facishare.crm.fmcg.tpm.dao.mongo.po;

import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.crm.fmcg.tpm.web.contract.model.ActivityTypeVO;
import com.facishare.crm.fmcg.tpm.web.contract.model.IActivityType;
import com.facishare.crm.fmcg.tpm.web.utils.TPMI18Utils;
import com.facishare.paas.I18N;
import com.google.common.base.Strings;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.mongodb.morphia.annotations.Embedded;
import org.mongodb.morphia.annotations.Entity;
import org.mongodb.morphia.annotations.Property;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * activity type po
 * create by @yangqf
 * create time 2021/11/15 16:00
 */
@Entity(value = "fmcg_tpm_activity_type", noClassnameStored = true)
@Data
@ToString
@EqualsAndHashCode(callSuper = true)
public class ActivityTypePO extends MongoPO implements IActivityType {

    // basic information
    public static final String F_NAME = "name";
    public static final String F_TEMPLATE_ID = "template_id";
    public static final String F_API_NAME = "api_name";
    public static final String F_DESCRIPTION = "description";
    public static final String F_VERSION = "version";
    public static final String F_PACKAGE = "package";
    public static final String F_STATUS = "status";
    public static final String F_EXCEPTION_STATUS = "exception_status";
    public static final String F_EXCEPTION_COUNT = "exception_count";

    // use scope
    public static final String F_EMPLOYEE_IDS = "employee_ids";
    public static final String F_DEPARTMENT_IDS = "department_ids";
    public static final String F_ROLE_IDS = "role_ids";
    public static final String F_ALL_EMPLOYEE_IDS = "all_employee_ids";
    public static final String F_SCOPE_DESCRIPTION = "scope_description";
    public static final String F_ACTIVITY_NODES = "activity_nodes";

    public static final String F_FORBID_RELATE_CUSTOMER = "forbid_relate_customer";

    public static final String F_TEMPLATE_NAME = "template_name";

    @Property(F_NAME)
    private String name;

    @Property(F_TEMPLATE_ID)
    private String templateId;

    @Property(F_API_NAME)
    private String apiName;

    @Property(F_DESCRIPTION)
    private String description;

    @Property(F_VERSION)
    private long version;

    @Property(F_PACKAGE)
    private String packageType;

    @Property(F_STATUS)
    private String status;

    @Property(F_EXCEPTION_STATUS)
    private String exceptionStatus;

    @Property(F_EXCEPTION_COUNT)
    private Integer exceptionCount = 0;

    @Property(F_EMPLOYEE_IDS)
    private List<Integer> employeeIds;

    @Property(F_DEPARTMENT_IDS)
    private List<Integer> departmentIds;

    @Property(F_ROLE_IDS)
    private List<String> roleIds;

    @Property(F_ALL_EMPLOYEE_IDS)
    private List<Integer> allEmployeeIds;

    @Property(F_SCOPE_DESCRIPTION)
    private String scopeDescription;

    @Embedded(F_ACTIVITY_NODES)
    private List<ActivityNodeEntity> activityNodes;

    @Property(F_FORBID_RELATE_CUSTOMER)
    private Boolean forbidRelateCustomer;

    @Property(F_TEMPLATE_NAME)
    private String templateName;

    public String getTemplateId() {
        if (Objects.nonNull(templateId)) {
            return templateId;
        }
        if (PreActivityType.TYPE_ACTIVITY.apiName().equals(apiName)) {
            return "display.complex";
        } else if (PreActivityType.TYPE_ACTIVITY_LIVE.apiName().equals(apiName)) {
            return "online.simple";
        }
        return "custom";
    }

    public static ActivityTypePO fromVO(ActivityTypeVO vo) {
        if (vo == null) {
            return null;
        }
        ActivityTypePO po = new ActivityTypePO();
        po.setName(vo.getName());
        po.setTemplateId(Strings.isNullOrEmpty(vo.getTemplateId()) ? "custom" : vo.getTemplateId());
        po.setUniqueId(vo.getId());
        po.setApiName(vo.getApiName());
        po.setDescription(vo.getDescription());
        po.setVersion(vo.getVersion());
        po.setPackageType(PackageType.CUSTOM.value());
        po.setStatus(vo.getStatus());
        po.setExceptionStatus(vo.getExceptionStatus() == null ? StatusType.NORMAL.value() : vo.getExceptionStatus());
        po.setExceptionCount(0);
        po.setEmployeeIds(vo.getEmployeeIds());
        po.setDepartmentIds(vo.getDepartmentIds());
        po.setRoleIds(vo.getRoleIds());
        po.setForbidRelateCustomer(vo.getForbidRelateCustomer());
        po.setScopeDescription(vo.getScopeDescription());
        po.setTemplateName(vo.getTemplateName());
        po.setActivityNodes(vo.getActivityNodeList().stream().map(ActivityNodeEntity::fromVO).collect(Collectors.toList()));
        return po;
    }

    public static ActivityTypeDraftBoxPO fromDraftBox(ActivityTypeVO vo) {
        if (vo == null) {
            return null;
        }
        ActivityTypeDraftBoxPO po = new ActivityTypeDraftBoxPO();
        po.setName(vo.getName());
        po.setTemplateId(Strings.isNullOrEmpty(vo.getTemplateId()) ? "custom" : vo.getTemplateId());
        po.setUniqueId(vo.getId());
        po.setApiName(vo.getApiName());
        po.setDescription(vo.getDescription());
        po.setVersion(vo.getVersion());
        po.setPackageType(PackageType.CUSTOM.value());
        po.setStatus(vo.getStatus());
        po.setExceptionStatus(vo.getExceptionStatus() == null ? StatusType.NORMAL.value() : vo.getExceptionStatus());
        po.setEmployeeIds(vo.getEmployeeIds());
        po.setDepartmentIds(vo.getDepartmentIds());
        po.setRoleIds(vo.getRoleIds());
        po.setScopeDescription(vo.getScopeDescription());
        po.setMustRelatedCustomer(vo.getForbidRelateCustomer());
        po.setTemplateName(vo.getTemplateName());
        po.setActivityNodes(vo.getActivityNodeList().stream().map(ActivityNodeEntity::fromVO).collect(Collectors.toList()));
        return po;
    }


    @Override
    public String getName() {
        if (!Strings.isNullOrEmpty(name)) {
            return name;
        }
        if (Objects.isNull(getId()) || Strings.isNullOrEmpty(super.getTenantId())) {
            return name;
        }
        String text = TPMI18Utils.getActivitySystemTypeText(super.getTenantId(), getId().toString(), this.apiName);
        if (!Strings.isNullOrEmpty(text)) {
            name = text;
        }
        return name;
    }

    @Override
    public String getDescription() {
        if (!Strings.isNullOrEmpty(description)) {
            if ("系统预置".equals(description)) { //ignorei18n
                return I18N.text(I18NKeys.FMCG_TPM_PRE_ACTIVITY_TYPE);
            }
        }
        return description;
    }
}
