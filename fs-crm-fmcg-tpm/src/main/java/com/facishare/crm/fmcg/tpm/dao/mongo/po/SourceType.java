package com.facishare.crm.fmcg.tpm.dao.mongo.po;

import com.facishare.crm.fmcg.common.apiname.ApiNames;

public enum SourceType {

        ACTIVITY(ApiNames.TPM_ACTIVITY_OBJ, "activity"),

        AGREEMENT(ApiNames.TPM_ACTIVITY_AGREEMENT_OBJ, "agreement");
        private final String name;
        private final String value;

        SourceType(String name, String value) {
            this.name = name;
            this.value = value;
        }

        public static String getValueByName(String name) {
            for (SourceType sourceType : SourceType.values()) {
                if (sourceType.name.equals(name)) {
                    return sourceType.value;
                }
            }
            return null;
        }
    }