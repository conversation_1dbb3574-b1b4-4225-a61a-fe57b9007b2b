package com.facishare.crm.fmcg.tpm.web.contract.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2022/10/14 15:22
 */
@Data
@ToString
public class FreezeItemVO implements Serializable {

    @JSONField(name = "budget_account_id")
    @JsonProperty(value = "budget_account_id")
    @SerializedName("budget_account_id")
    private String budgetAccountId;

    @JSONField(name = "amount")
    @JsonProperty(value = "amount")
    @SerializedName("amount")
    private BigDecimal amount;
}
