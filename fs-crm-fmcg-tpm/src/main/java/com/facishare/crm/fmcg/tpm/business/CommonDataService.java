package com.facishare.crm.fmcg.tpm.business;

import com.google.common.collect.Lists;
import com.facishare.crm.fmcg.common.apiname.CommonFields;
import com.facishare.crm.fmcg.tpm.business.abstraction.ICommonDataService;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2022/8/10 上午11:07
 */
@Component
public class CommonDataService implements ICommonDataService {

    @Resource
    private ServiceFacade serviceFacade;

    @Override
    public boolean existsData(User user, String apiName, String dataId) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(1);
        query.setOffset(0);
        query.setNeedReturnQuote(false);
        query.setNeedReturnCountNum(false);
        query.setSearchSource("db");
        Filter idFilter = new Filter();
        idFilter.setFieldName(CommonFields.ID);
        idFilter.setOperator(Operator.EQ);
        idFilter.setFieldValues(Lists.newArrayList(dataId));
        query.setFilters(Lists.newArrayList(idFilter));

        return !serviceFacade.findBySearchQuery(user, apiName, query).getData().isEmpty();
    }
}
