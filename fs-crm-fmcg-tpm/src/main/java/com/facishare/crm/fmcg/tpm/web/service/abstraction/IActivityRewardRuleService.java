package com.facishare.crm.fmcg.tpm.web.service.abstraction;

import com.facishare.crm.fmcg.tpm.api.rule.AddActivityRewardRule;
import com.facishare.crm.fmcg.tpm.api.rule.DeleteRewardRule;
import com.facishare.crm.fmcg.tpm.api.rule.GetActivityRewardRule;
import com.facishare.crm.fmcg.tpm.api.rule.UpdateRewardRule;

/**
 * Author: linmj
 * Date: 2023/9/15 17:22
 */
public interface IActivityRewardRuleService {

    AddActivityRewardRule.Result add(AddActivityRewardRule.Arg arg);

    GetActivityRewardRule.Result get(GetActivityRewardRule.Arg arg);

    UpdateRewardRule.Result update(UpdateRewardRule.Arg arg);

    DeleteRewardRule.Result delete(DeleteRewardRule.Arg arg);
}
