@startuml BudgetServiceClassDiagram

' 定义接口和实现类的样式
skinparam class {
    BackgroundColor<<Interface>> LightBlue
    BorderColor<<Interface>> Blue
}

interface IBudgetConsumeV2Service {
    +dealActionTriggerBefore(DealTriggerAction.Arg)
    +dealActionTriggerAfter(DealTriggerAction.Arg)
    +dealActionFinallyDo(DealTriggerAction.Arg)
    +dealFlowCompletedBefore(DealApprovalAction.Arg)
    +dealFlowCompletedAfter(DealApprovalAction.Arg)
}

class BudgetConsumeV2Service implements IBudgetConsumeV2Service {
    ' 依赖注入的服务
    -ServiceFacade serviceFacade
    -BudgetNewConsumeRuleDAO budgetNewConsumeRuleDAO
    -BudgetAccountService budgetAccountService
    -TransactionProxy transactionProxy
    -BudgetAccountDetailService budgetAccountDetailService
    -RedisLockService redisLockService
    -IBudgetOccupyService budgetOccupyService
    -BudgetCalculateService budgetCalculateService
    -IFiscalTimeService fiscalTimeService
    -BudgetTypeDAO budgetTypeDAO
    -IBudgetCompareService budgetCompareService
    -IBudgetSubjectService budgetSubjectService

    ' 核心业务方法
    +dealActionTriggerBefore(DealTriggerAction.Arg)
    +dealActionTriggerAfter(DealTriggerAction.Arg)
    +dealActionFinallyDo(DealTriggerAction.Arg)
    +dealFlowCompletedBefore(DealApprovalAction.Arg)
    +dealFlowCompletedAfter(DealApprovalAction.Arg)
    -getConsumeRuleByMasterData(String, IObjectData)
    -validateBudgetLevel(String, IObjectData, BudgetTableProvisionEntity)
    -getWithholdingAmount(User, String, IObjectData, IObjectData, BudgetTableFrozenEntity, BudgetNewConsumeRulePO, String, String)
    -getDirectDeductBudgetAndAmount(User, BudgetTableDeDuctEntity, IObjectData, Map)
}

interface IBudgetOperator {
    +validateConsumableAmount(BigDecimal)
    +freeze(BigDecimal)
    +unfreeze(BigDecimal, BizType)
    +expenditure(BigDecimal)
    +income(BigDecimal)
    +tryLock()
    +unlock()
}

class BudgetOperatorFactory {
    +{static} initOperator(BizType, User, String, String, String)
    +{static} initOperator(BizType, User, String, String, String, IObjectData)
}

' 相关的数据类
class BudgetConsumeSession {
    -String id
    +setAttribute(String, String)
    +getAttribute(String)
    +destroy()
}

enum BizType {
    CONSUME
    INVALID_BACK
    FREEZE
    UNFREEZE
}

' 关系定义
BudgetConsumeV2Service ..> IBudgetOperator : uses
BudgetConsumeV2Service ..> BudgetOperatorFactory : creates
BudgetConsumeV2Service ..> BudgetConsumeSession : uses
BudgetConsumeV2Service ..> BizType : uses
BudgetOperatorFactory ..> IBudgetOperator : creates

@enduml