package com.facishare.crm.fmcg.reward;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.fmcg.common.adapter.abstraction.IEnterpriseConnectionService;
import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.common.apiname.CommonFields;
import com.facishare.crm.fmcg.common.apiname.RedPacketRecordObjFields;
import com.facishare.crm.fmcg.common.utils.SearchQueryUtil;
import com.facishare.crm.fmcg.tpm.api.MengNiuTenantInformation;
import com.facishare.crm.fmcg.tpm.api.rule.GetRuleDescribe;
import com.facishare.crm.fmcg.tpm.api.rule.RewardRuleDTO;
import com.facishare.crm.fmcg.tpm.api.rule.UpdateRewardRule;
import com.facishare.crm.fmcg.tpm.api.scan.BigDatePay;
import com.facishare.crm.fmcg.tpm.api.scan.BigDateScanCode;
import com.facishare.crm.fmcg.tpm.business.RedPacketService;
import com.facishare.crm.fmcg.tpm.business.RewardExceptionRecordService;
import com.facishare.crm.fmcg.tpm.dao.mongo.ActivityRewardRuleDAO;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityRewardRulePO;
import com.facishare.crm.fmcg.tpm.web.manager.abstraction.IRewardRuleManager;
import com.facishare.crm.fmcg.tpm.BaseTest;
import com.facishare.crm.fmcg.tpm.web.service.ScanCodeService;
import com.facishare.crm.fmcg.tpm.web.service.TenantHierarchyService;
import com.facishare.crm.fmcg.tpm.web.service.abstraction.IActivityRewardRuleService;
import com.facishare.crm.fmcg.tpm.web.service.abstraction.IPayCallbackService;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.SearchTemplateQueryExt;
import com.facishare.paas.appframework.metadata.dto.SaveMasterAndDetailData;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.dao.pg.mapper.metadata.SpecialTableMapper;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.fxiaoke.api.IdGenerator;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.junit.Test;
import org.springframework.util.ResourceUtils;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.nio.file.Files;
import java.util.HashMap;
import java.util.Map;

/**
 * Author: linmj
 * Date: 2023/9/15 16:46
 */
public class RewardTest extends BaseTest {

    @Resource
    private IRewardRuleManager rewardRuleManager;

    @Resource
    private ScanCodeService scanCodeService;

    @Resource
    private IEnterpriseConnectionService enterpriseConnectionService;

    @Resource
    private ServiceFacade serviceFacade;

    @Resource
    private IPayCallbackService payCallbackService;

    @Resource
    private RedPacketService redPacketService;

    @Resource
    private SpecialTableMapper specialTableMapper;

    @Resource
    private ActivityRewardRuleDAO activityRewardRuleDAO;


    @Resource
    private RewardExceptionRecordService rewardExceptionRecordService;

    @Resource
    private IActivityRewardRuleService activityRewardRuleService;
    @Resource
    private TenantHierarchyService tenantHierarchyService;

    @Test
    public void testGetDescribe() {
//        System.out.println(JSON.toJSONString(rewardRuleManager.getRewardDescribe("84931", new GetRuleDescribe.Arg())));

        MengNiuTenantInformation tenant = tenantHierarchyService.load("40164897");
        System.out.println(tenant.getTenantId());
        System.out.println(tenant.getRole());
    }


    @Test
    public void testScanCode() {
        BigDateScanCode.Arg arg = new BigDateScanCode.Arg();
        //arg.setCode("1CE769CA25D43E12BFD325B650135D5813ACF658BB9186A098355D1854DB08F9");
        arg.setUrl("/133000000006000032");
        arg.setEnvironment("default");
        System.out.println(JSON.toJSONString(scanCodeService.bigDateScanCode(arg)));
    }

    @Test
    public void testPay() {
        BigDatePay.Arg arg = new BigDatePay.Arg();
        arg.setEnvironment("default");
        arg.setCodes(Lists.newArrayList("eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJDT0RFIjoiNjUyM2JlNGMxY2VkMzYwMDAxZTc1NjQzLjY1MjUwOTY1NGI0ZWZhMDAwMTZlZjJhMC44Mjk1OCM2NGU4OTAxOTE1YzM1MzAwMDEwNGZjMDYuNjRlODg5MzMxNWMzNTMwMDAxMDFhZjZmIiwiZXhwIjoxNjk3NTQyNTIxfQ.XcKz4GtjaizOjzDSRoqb9EgbDKOwf8Zu-MqYdUJpcCs"));
        arg.setAppId("wxf70ac4798732b66f");
        arg.setToken("o3TSW4gFGu46J7dT6MGTvp3SsxU0");
        arg.setPhoneToken("1");
        System.out.println(JSON.toJSONString(scanCodeService.bigDatePay(arg)));
    }

    @Test
    public void testGetStore() {
        System.out.println(enterpriseConnectionService.getStoreId("82958", "89386"));
    }

    @Test
    public void setRole() {
        System.out.println(JSON.toJSONString(serviceFacade.getUserRole(User.builder().outTenantId("").build())));
    }

    @Test
    public void testPayCallback() {

        payCallbackService.dealPayCallback("{\"nonce\":\"26e6c1cc-7678-4b2b-8c9f-333c5b936d00\",\"timeStamp\":\"1697537826262\",\"content\":\"n9zp5oOyR3aJrUbd10Ltn9t9ohWaJ7Asn6vdB6vTqSogRCmNsbwmmtRqAn/HrG6rHHwxo+qar42//9Bz9N/5belgXkXVvSMV/RIFE+CSpGsyv8Dzr80fx/2L5YDCnOt4RV01yWI9KL1GNuyCdz8H+bgJEUiBTAKmfidzay+7JtprKRVhPvA+JuRrlIRY1OlQ+r00nl4lUaJ8cE/ZTUIvmv9K5iWmhXUYHs0BX79Pnj/8bL6cbty5ER65o9z3/vDSuyFqBXpW3Gb+1+IuZJ8sN1YrYClWH9P3sjD9b9z1pZd13qOcvTD8OQPHnsKvetQlt6dqUyOuTrUrx0009wjpHiSs6honA3SopgQ5E8qTJL5KJooCOjXqan0GtWOJZw+OrccF4RVuSHoELf/pnFcDd4nGn7cJPasKBKqCVmU9pcJ4FavXm9ViIwMIXkU/6Ae4KF+S5n3n31E+Jr32MLSa05gxSztaIw+P0rU2v2PLxEQkl7IcLcig8pZQxhJB/HHorEUjsFLXVt4oB08PrWwmJTGm8MXAg/5BObDMzmAfN/wlPb+HtFI92/tjEosACZtOjBw7BEAcY+CIC/9HM93DuNchaD5QfmvlcQydEnSKgK2zISdZ9S51JjGrUVRdYBRA\",\"sig\":\"961d7e3f7beb72b376553520dec0e6db3a224241\"}");
    }

    @Test
    public void testGetLevel() {
        System.out.println(enterpriseConnectionService.getLevel("89714", "84931", Sets.newHashSet("89714","84931")));
        System.out.println(enterpriseConnectionService.getLevel("82958", "89386", Sets.newHashSet("89386")));
    }

    @Test
    public void testTransfer() {
        IObjectData red = serviceFacade.findObjectData(User.systemUser("82958"), "6617afb574aa751db0f0d39c", ApiNames.RED_PACKET_RECORD_OBJ);
        redPacketService.refreshRewardStatus(red);
    }

    @Test
    public void addField() {
        specialTableMapper.setTenantId("82958").insertBySql("ALTER TABLE IF EXISTS fmcg_red_packet_record ADD COLUMN IF NOT EXISTS test_field varchar(200);");
    }

    @Test
    public void testUpdate() {
        String tenantId = "82958";
        ActivityRewardRulePO activityRewardRulePO = activityRewardRuleDAO.get(tenantId, "65221a78647e3500016a9c12");
        activityRewardRulePO.setRuleType("2");
        activityRewardRuleDAO.edit(tenantId, -10000, activityRewardRulePO.getUniqueId(), activityRewardRulePO);
    }

    @Test
    public void testReward() {
        //rewardRuleRewardService.bigDateReward("SP:6539E31C366B740001E43BB6");
    }


    @Test
    public void addRedPacket() {
        IObjectData master = new ObjectData();
        String tenantId = "89150";
        master.setTenantId(tenantId);
        master.setId(IdGenerator.get());
        master.setDescribeApiName(ApiNames.RED_PACKET_RECORD_OBJ);
        master.setRecordType(CommonFields.RECORD_TYPE__DEFAULT);
        master.setOwner(Lists.newArrayList("-10000"));
        master.set(RedPacketRecordObjFields.REWARDED_PERSON_ID, "1.1000");
        Map<String, IObjectDescribe> describeMap = new HashMap<>();
        describeMap.put(ApiNames.RED_PACKET_RECORD_DETAIL_OBJ, serviceFacade.findObject(tenantId, ApiNames.RED_PACKET_RECORD_DETAIL_OBJ));
        SaveMasterAndDetailData.Arg saveArg = SaveMasterAndDetailData.Arg.builder().masterObjectData(master).objectDescribes(describeMap).build();
        serviceFacade.saveMasterAndDetailData(User.systemUser(tenantId), saveArg);

    }

    @Test
    public void testCreateOBJ() {
        String json;
        String listLayout;
        try {
            File file = ResourceUtils.getFile("classpath:tpm/module_reward/RewardExceptionRecordObj__c.json");
            json = new String(Files.readAllBytes(file.toPath()));
            listLayout = new String(Files.readAllBytes(ResourceUtils.getFile("classpath:tpm/module_reward/RewardExceptionRecordObj__cLayout.json").toPath()));
        } catch (FileNotFoundException e) {
            throw new MetaDataBusinessException("file not found.");
        } catch (IOException ex) {
            throw new MetaDataBusinessException("resource failed.");
        }
        System.out.println(serviceFacade.createDescribe(User.systemUser("84931"), json, null, listLayout, true, false));
    }

    @Test
    public void testWriteError() {

        rewardExceptionRecordService.writeRecord("82958", "123", "大日期", "null pointer2", "2");
    }

    @Test
    public void testEditRule() {
        UpdateRewardRule.Arg arg = new UpdateRewardRule.Arg();
        arg.setTenantId("82958");
        arg.setRewardRule(JSON.parseObject("{\"reward_details\":[{\"reward_node\":{\"reward_dimension\":\"1\",\"reward_type\":\"by_designee\",\"reward_action\":\"\",\"reward_target\":\"enterprise_boss\"},\"reward_strategy\":{\"reward_method\":\"red_packet\",\"reward_method_type\":\"solid_red_packet\",\"reward_quantity\":0.2,\"reward_remark\":\"提现测试2\",\"distribute_method\":\"withdraw\",\"random_reward_levels\":[],\"expired_days\":10},\"reward_payment\":{\"pay_account\":{\"account_type\":\"main_account\"}}},{\"reward_node\":{\"reward_dimension\":\"1\",\"reward_type\":\"by_biz_action\",\"reward_action\":\"652112a205f1565148bee94a\",\"reward_target\":\"scan_executor\"},\"reward_strategy\":{\"reward_method\":\"red_packet\",\"reward_method_type\":\"solid_red_packet\",\"reward_quantity\":0.1,\"reward_remark\":\"提现测试\",\"distribute_method\":\"withdraw\",\"random_reward_levels\":[],\"expired_days\":null},\"detail_id\":\"656ef6a093117400018f9aa3\",\"reward_payment\":{\"pay_account\":{\"account_type\":\"main_account\"}}},{\"reward_node\":{\"reward_dimension\":\"consumer\",\"reward_type\":\"by_biz_action\",\"reward_action\":\"652f7faa835ee53e7a159743\",\"reward_target\":\"scan_executor\"},\"reward_strategy\":{\"reward_method\":\"reduced_payment\",\"reward_method_type\":\"solid_red_packet\",\"reward_quantity\":\"\",\"reward_remark\":\"\",\"distribute_method\":\"now\",\"random_reward_levels\":[],\"expired_days\":null},\"detail_id\":\"656ef6a093117400018f9aa4\",\"reward_payment\":{\"receive_account\":{\"account_type\":\"store_dealer\"}}}],\"trigger_type\":\"652f7faa835ee53e7a159743\",\"related_object_api_name\":\"TPMActivityObj\",\"related_object_id\":\"656ef69a93117400018f99ec\",\"rule_type\":\"reward.big_date\",\"id\":\"656ef6a093117400018f9aa2\",\"tenant_id\":\"82958\",\"last_updater\":-10000,\"create_time\":*************,\"last_update_time\":*************,\"unique_id\":\"656ef6a093117400018f9aa2\"}", RewardRuleDTO.class));
        System.out.println(activityRewardRuleService.update(arg));
    }

    @Test
    public void testCount() {
        SearchTemplateQuery query = SearchQueryUtil.formSimpleQuery(0, 200, Lists.newArrayList());
        SearchTemplateQueryExt queryExt = SearchTemplateQueryExt.of(query);
        queryExt.onlyQueryTotalNumIgnorePermission();
        int count = serviceFacade.findBySearchQueryWithFieldsIgnoreAll(User.systemUser("84931"), ApiNames.ACCOUNT_OBJ, queryExt.toSearchTemplateQuery(), Lists.newArrayList(CommonFields.ID)).getTotalNumber();
        System.out.println(count);
    }

}
