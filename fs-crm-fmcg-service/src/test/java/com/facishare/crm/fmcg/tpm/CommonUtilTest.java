package com.facishare.crm.fmcg.tpm;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.common.apiname.TPMActivityFields;
import com.facishare.crm.fmcg.tpm.utils.CommonUtils;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/2/28 下午4:48
 */
public class CommonUtilTest extends BaseTest{

    @Resource
    private ServiceFacade serviceFacade;

    @Test
    public void testQuery1(){
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(-1);
        Filter filter = new Filter();
        filter.setFieldName(TPMActivityFields.ACTIVITY_STATUS);
        filter.setFieldValues(Lists.newArrayList(TPMActivityFields.ACTIVITY_STATUS__END));
        filter.setOperator(Operator.EQ);
        query.setFilters(Lists.newArrayList(filter));
        query.setPattern("1");
        List<IObjectData> data = CommonUtils.queryAllDataInFields(serviceFacade, User.systemUser("84931"), ApiNames.TPM_ACTIVITY_OBJ,query,Lists.newArrayList("name"));
        System.out.println(data.size());
        System.out.println(JSON.toJSONString(data));
    }

    @Test
    public void testQuery2(){
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(-1);
        Filter filter = new Filter();
        filter.setFieldName(TPMActivityFields.ACTIVITY_STATUS);
        filter.setFieldValues(Lists.newArrayList(TPMActivityFields.ACTIVITY_STATUS__END));
        filter.setOperator(Operator.EQ);
        query.setFilters(Lists.newArrayList(filter));
        List<IObjectData> data = CommonUtils.queryAllDataInFields(serviceFacade, User.systemUser("84931"), ApiNames.TPM_ACTIVITY_OBJ,query,Lists.newArrayList("name"));
        System.out.println(data.size());
        System.out.println(JSON.toJSONString(data));
    }
}
