package com.facishare.crm.fmcg.tpm;
import com.google.common.collect.Lists;

import com.facishare.crm.fmcg.tpm.action.TPMDealerActivityCostObjUpdateImportTemplateAction;
import com.facishare.paas.appframework.common.util.StopWatch;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.core.predef.action.BaseImportTemplateAction;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.Optional;

/**
 * author: wuyx
 * description:
 * createTime: 2023/5/5 18:08
 */
public class TPMDealerActivityCostObjTest extends BaseTest {

    @Resource
    private ServiceFacade serviceFacade;

    @Test
    public void updateImportTemplateActionTest() {
        TPMDealerActivityCostObjUpdateImportTemplateAction action = new TPMDealerActivityCostObjUpdateImportTemplateAction();
        action.setServiceFacade(serviceFacade);
       // action.setStopWatch(StopWatch.create("TPMDealerActivityCostObjUpdateImportTemplateAction"));

        RequestContext requestContext = RequestContext.builder()
                .tenantId("85494")
                .ea("85494")
                .user(User.systemUser("85494"))
                .build();
        action.setActionContext(new ActionContext(requestContext, "TPMDealerActivityCostObj", "UpdateImportTemplate"));

        BaseImportTemplateAction.Arg arg = new BaseImportTemplateAction.Arg();
        arg.setDescribeApiName("");
        arg.setImportType(1);
        arg.setMatchingType(2);
        arg.setUnionImportApiNameList(Lists.newArrayList());
        arg.setObjectCode("TPMDealerActivityCostObj");
        arg.setRelatedApiNameList(Lists.newArrayList());
        arg.setType(null);
        arg.setRecordType(null);
        arg.setDetailRecordType("");
        arg.setDetailArg(Lists.newArrayList());

        action.setArg(arg);

        BaseImportTemplateAction.Result result = action.act(arg);

        assert result != null;
    }
}
