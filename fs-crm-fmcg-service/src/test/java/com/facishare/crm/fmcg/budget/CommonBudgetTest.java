package com.facishare.crm.fmcg.budget;

import com.facishare.crm.fmcg.tpm.BaseTest;
import com.facishare.crm.fmcg.tpm.web.contract.kk.Cancel;
import com.facishare.crm.fmcg.tpm.web.contract.kk.Freeze;
import com.facishare.crm.fmcg.tpm.web.custom.abstraction.ICommonBudgetService;
import com.google.common.collect.Lists;
import org.junit.Test;

import javax.annotation.Resource;
import java.math.BigDecimal;

public class CommonBudgetTest extends BaseTest {

    @Resource
    private ICommonBudgetService commonBudgetService;

    @Test
    public void freezeAndCancelTest() {
        String bizApiName = "AccountObj";
        String bizDataId = "6407f806dbfb1e0001ee4bb6";
        String accountId = "6488510d485ac0000174bb70";

        Freeze.Arg freezeArg = new Freeze.Arg();

        freezeArg.setApiName(bizApiName);
        freezeArg.setDataId(bizDataId);

        Freeze.FreezeItemVO freezeItem = new Freeze.FreezeItemVO();
        freezeItem.setAccountId(accountId);
        freezeItem.setAmount(new BigDecimal("1"));

        freezeArg.setFreezeData(Lists.newArrayList(freezeItem));

        Freeze.Result freezeResult = commonBudgetService.freeze(freezeArg);

        assert freezeResult != null;

        Cancel.Arg cancelArg = new Cancel.Arg();

        cancelArg.setApiName(bizApiName);
        cancelArg.setDataId(bizDataId);

        Cancel.Result cancelResult = commonBudgetService.cancel(cancelArg);

        assert cancelResult != null;
    }

    @Test
    public void cancelTest() {
        String bizApiName = "AccountObj";
        String bizDataId = "6407f806dbfb1e0001ee4bb6";

        Cancel.Arg cancelArg = new Cancel.Arg();

        cancelArg.setApiName(bizApiName);
        cancelArg.setDataId(bizDataId);

        Cancel.Result cancelResult = commonBudgetService.cancel(cancelArg);

        assert cancelResult != null;
    }
}
