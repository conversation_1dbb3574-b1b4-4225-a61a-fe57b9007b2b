package com.facishare.crm.fmcg.pg;

import com.facishare.crm.fmcg.tpm.BaseTest;
import com.facishare.paas.metadata.dao.pg.mapper.metadata.FieldMapper;
import com.facishare.paas.metadata.dao.pg.mapper.metadata.SpecialTableMapper;
import com.facishare.paas.pod.client.DbRouterClient;
import com.facishare.paas.pod.dto.RouterInfo;
import com.github.mybatis.util.InjectSchemaUtil;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * Author: linmj
 * Date: 2023/11/14 15:31
 */
public class PGTest extends BaseTest {

    @Resource
    private FieldMapper fieldMapper;

    @Resource
    private SpecialTableMapper specialTableMapper;

    @Resource
    private DbRouterClient dbRouterClient;


    @Test
    public void addField(){

        specialTableMapper.setTenantId("84931").batchUpdateBySql("alter table fmcg_red_packet_record add column if not exists  test_c varchar(128) ");
    }

    @Test
    public void testRouter(){
        String tenantId = "79271";
        String sql = "alter table fmcg_red_packet_record add column  test_c varchar(128)";
        final RouterInfo routerInfo = dbRouterClient.queryRouterInfo(tenantId, "CRM", "fs-metadata-service", "postgresql");
        if (Objects.equals(routerInfo.getStandalone(), Boolean.TRUE)) {
            System.out.println( InjectSchemaUtil.injectSchema(sql, "postgresql", "sch_" + tenantId));
        }
        System.out.println(sql);
    }
}
