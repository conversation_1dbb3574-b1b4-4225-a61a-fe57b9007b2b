package com.facishare.crm.fmcg.service.web.inner;

import com.facishare.crm.fmcg.dms.model.RecalculateOldData;
import com.facishare.crm.fmcg.dms.model.RecalculateSingleData;
import com.facishare.crm.fmcg.dms.model.RetryConvert;
import com.facishare.crm.fmcg.dms.model.RetryMatch;
import com.facishare.crm.fmcg.dms.web.abstraction.IAccountsPayableService;
import com.facishare.crm.fmcg.dms.web.abstraction.IAccountsReceivableNoteService;
import com.facishare.crm.fmcg.dms.web.abstraction.IRebateService;
import com.facishare.crm.fmcg.dms.web.abstraction.IUpgradeService;
import com.facishare.crm.fmcg.service.web.inner.provider.InnerApiResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping(value = "/TPM/DMS", produces = "application/json")
public class InnerDMSController {

    @Resource
    private IAccountsReceivableNoteService accountsReceivableNoteService;

    @Resource
    private IUpgradeService upgradeService;
    @Resource
    private IAccountsPayableService accountsPayableService;
    @Resource
    private IRebateService rebateService;

    @PostMapping(value = "/InitField")
    public InnerApiResult<String> init(@RequestParam List<Integer> tenantIds) {
        try {
            accountsReceivableNoteService.initField(tenantIds);
        } catch (Exception e) {
            log.info("init field error", e);
            return new InnerApiResult<>(500, e.getMessage());
        }
        return new InnerApiResult<>("success");
    }

    @PostMapping(value = "/InitRefundButton")
    public InnerApiResult<String> initRefundButton(@RequestParam List<Integer> tenantIds) {
        try {
            accountsReceivableNoteService.initRefundButton(tenantIds);
        } catch (Exception e) {
            log.info("init field error", e);
            return new InnerApiResult<>(500, e.getMessage());
        }
        return new InnerApiResult<>("success");
    }

    @PostMapping(value = "/RetryMatch")
    public InnerApiResult<RetryMatch.Result> retryMatch(@RequestBody RetryMatch.Arg arg) {
        return InnerApiResult.apply(accountsReceivableNoteService::retryMatch, arg);
    }

    @PostMapping(value = "/RetryConvert")
    public InnerApiResult<RetryConvert.Result> retryConvert(@RequestBody RetryConvert.Arg arg) {
        return InnerApiResult.apply(accountsReceivableNoteService::retryConvert, arg);
    }

    @PostMapping(value = "/RecalculateOldData")
    public InnerApiResult<RecalculateOldData.Result> recalculateOldData(@RequestBody RecalculateOldData.Arg arg) {
        return InnerApiResult.apply(upgradeService::recalculateOldData, arg);
    }

    @PostMapping(value = "/RecalculateOldReceivableData")
    public InnerApiResult<RecalculateSingleData.Result> recalculateOldReceivableData(@RequestBody RecalculateSingleData.Arg arg) {
        return InnerApiResult.apply(upgradeService::recalculateOldReceivableData, arg);
    }

    @PostMapping(value = "/RecalculateOldMatchNoteData")
    public InnerApiResult<RecalculateSingleData.Result> recalculateOldMatchNoteData(@RequestBody RecalculateSingleData.Arg arg) {
        return InnerApiResult.apply(upgradeService::recalculateOldMatchNoteData, arg);
    }

    @PostMapping(value = "/updateStatus")
    public String recalculateOldMatchNoteData(@RequestParam String tenantId, @RequestParam Integer status) {
        return accountsPayableService.updateStatus(tenantId, status);
    }

    @PostMapping(value = "/InitFieldV2")
    public InnerApiResult<String> initFieldV2(@RequestParam List<Integer> tenantIds, @RequestParam String flag) {
        try {
            accountsPayableService.initField(tenantIds, flag);
        } catch (Exception e) {
            log.info("init field error", e);
            return new InnerApiResult<>(500, e.getMessage());
        }
        return new InnerApiResult<>("success");
    }


    @PostMapping(value = "/InitManualMatchLayout")
    public InnerApiResult<String> initManualMatchLayout(@RequestParam List<Integer> tenantIds, @RequestParam(required = false) String flag) {
        try {
            accountsPayableService.initManualMatchLayout(tenantIds, flag);
        } catch (Exception e) {
            log.info("init field error", e);
            return new InnerApiResult<>(500, e.getMessage());
        }
        return new InnerApiResult<>("success");
    }

    @PostMapping(value = "/BatchAccountsPayableEnable")
    public InnerApiResult<String> batchAccountsPayableEnable(@RequestParam List<Integer> tenantIds, @RequestParam String flag, @RequestParam(required = false) String enablePayable) {
        try {
            accountsPayableService.batchAccountsPayableEnable(tenantIds, flag, enablePayable);
        } catch (Exception e) {
            log.info("init field error", e);
            return new InnerApiResult<>(500, e.getMessage());
        }
        return new InnerApiResult<>("success");
    }

    @PostMapping(value = "/CreateOpeningBalanceSettingLayoutRule")
    public InnerApiResult<String> createOpeningBalanceSettingLayoutRule(@RequestParam List<Integer> tenantIds) {
        try {
            accountsPayableService.createOpeningBalanceSettingLayoutRule(tenantIds);
        } catch (Exception e) {
            log.info("CreateOpeningBalanceSettingLayoutRule error", e);
            return new InnerApiResult<>(500, e.getMessage());
        }
        return new InnerApiResult<>("success");
    }

    @PostMapping(value = "/BatchAccountsReceivableAutoMatchButtonEnable")
    public InnerApiResult<String> batchAccountsReceivableAutoMatchButtonEnable(@RequestParam List<Integer> tenantIds, @RequestParam String flag, @RequestParam String env) {
        try {
            accountsReceivableNoteService.batchEnableAutoMatch(tenantIds, flag, env);
        } catch (Exception e) {
            log.info("init field error", e);
            return new InnerApiResult<>(500, e.getMessage());
        }
        return new InnerApiResult<>("success");
    }

    @PostMapping(value = "/InitRebateButton")
    public InnerApiResult<String> initRebateButton(@RequestParam List<Integer> tenantIds) {
        try {
            rebateService.initButton(tenantIds.stream().map(String::valueOf).collect(Collectors.toList()));
        } catch (Exception e) {
            log.info("InitRebateButton error", e);
            return new InnerApiResult<>(500, e.getMessage());
        }
        return new InnerApiResult<>("success");
    }

    @PostMapping(value = "/InvalidMatchNoteWithRebateAccounts")
    public InnerApiResult<String> invalidMatchNoteWithRebateAccounts(@RequestParam(required = false) List<Integer> tenantIds, @RequestParam(required = false) String flag) {
        try {
            accountsReceivableNoteService.invalidMatchNoteWithRebateAccounts(tenantIds.stream().map(String::valueOf).collect(Collectors.toList()), flag);
        } catch (Exception e) {
            log.info("InvalidMatchNoteWithRebateAccounts error", e);
            return new InnerApiResult<>(500, e.getMessage());
        }
        return new InnerApiResult<>("success");
    }

    @PostMapping(value = "/fixMengniuPaymentMatch")
    public InnerApiResult<RetryMatch.Result> retryMatch(@RequestBody RetryMatch.Arg arg, @RequestParam Long begin, @RequestParam Long end) {
        try {
            accountsReceivableNoteService.fixMengniuPaymentMatch(arg, begin, end);
        } catch (Exception e) {
            log.info("fixMengniuPaymentMatch error", e);
            return new InnerApiResult<>(500, e.getMessage());
        }
        return new InnerApiResult<>(new RetryMatch.Result());
    }
}
