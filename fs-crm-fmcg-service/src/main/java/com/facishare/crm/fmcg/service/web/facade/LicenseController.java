package com.facishare.crm.fmcg.service.web.facade;

import com.facishare.crm.fmcg.tpm.web.contract.LicenseGet;
import com.facishare.crm.fmcg.tpm.web.service.abstraction.ILicenseService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;


@Slf4j
@RestController
@RequestMapping(value = "/DMS/License", produces = "application/json")
public class LicenseController {

    @Resource
    private ILicenseService dmsLicenseService;

    @PostMapping(value = "Get")
    public LicenseGet.Result get(@RequestBody LicenseGet.Arg arg) {
        return dmsLicenseService.get(arg);
    }
}