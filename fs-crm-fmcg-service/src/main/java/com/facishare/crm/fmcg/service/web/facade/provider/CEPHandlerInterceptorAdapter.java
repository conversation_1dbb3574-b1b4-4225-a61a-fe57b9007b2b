package com.facishare.crm.fmcg.service.web.facade.provider;

import com.facishare.crm.fmcg.common.http.ApiContext;
import com.facishare.crm.fmcg.common.http.ApiContextManager;
import com.facishare.crm.fmcg.tpm.web.service.abstraction.ITPMRelationService;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.rest.CEPXHeader;
import com.facishare.paas.appframework.core.util.Lang;

import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.annotation.Resource;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Objects;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2021/11/16 18:40
 */
@Slf4j
@SuppressWarnings("Duplicates")
public class CEPHandlerInterceptorAdapter extends HandlerInterceptorAdapter {

    @Resource
    private ITPMRelationService itpmRelationService;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        ApiContext context = loadContextFromRequest(request);
        ApiContextManager.setContext(context);
        return super.preHandle(request, response, handler);
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        ApiContextManager.removeContext();
        I18N.clearContext();
        super.afterCompletion(request, response, handler, ex);
    }

    private ApiContext loadContextFromRequest(HttpServletRequest request) {
        String tenantId = request.getHeader(CEPXHeader.TENANT_ID.key());
        String tenantAccount = request.getHeader(CEPXHeader.ENTERPRISE_ACCOUNT.key());

        String appId = request.getHeader(CEPXHeader.APP_ID.key());
        String postId = request.getHeader(CEPXHeader.POST_ID.key());
        String local = request.getHeader(CEPXHeader.LOCALE.key());
        if (!Strings.isNullOrEmpty(local)) {
            I18N.setContext(tenantId, local);

            RequestContextManager.removeContext();
            if (Objects.isNull(RequestContextManager.getContext())) {
                RequestContext requestContext = RequestContext.builder().tenantId(tenantId).user(User.systemUser(tenantId)).lang(Lang.of(local)).build();
                RequestContextManager.setContext(requestContext);
            }
        }

        String outTenantId = request.getHeader(CEPXHeader.OUT_TENANT_ID.key());
        String outUserId = request.getHeader(CEPXHeader.OUT_USER_ID.key());

        String employeeId = request.getHeader(CEPXHeader.USER_ID.key());
        Integer intEmployeeId = null;
        try {
            if (Strings.isNullOrEmpty(employeeId)) {
                String relationEmployeeId = itpmRelationService.getUpStreamAccountOwnId(ApiContext.builder().tenantId(tenantId).outTenantId(outTenantId).build());
                if (!Strings.isNullOrEmpty(relationEmployeeId)) {
                    employeeId = relationEmployeeId;
                }
            }

            intEmployeeId = Integer.parseInt(employeeId);


        } catch (Exception ex) {
            log.warn("try parse employee id from header error : {}", employeeId);
        }

        return ApiContext.builder()
                .tenantId(tenantId)
                .tenantAccount(tenantAccount)
                .employeeId(intEmployeeId)
                .appId(appId)
                .postId(postId)
                .outTenantId(outTenantId)
                .outUserId(outUserId)
                .build();
    }
}