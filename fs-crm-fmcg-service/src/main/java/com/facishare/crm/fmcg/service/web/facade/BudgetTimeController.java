package com.facishare.crm.fmcg.service.web.facade;

import com.facishare.crm.fmcg.tpm.web.contract.CalculateTimeSpan;
import com.facishare.crm.fmcg.tpm.web.service.abstraction.IBudgetTimeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2022/7/13 11:33
 */
@Slf4j
@RestController
@RequestMapping(value = "/TPM/BudgetTime", produces = "application/json")
public class BudgetTimeController {

    @Resource
    private IBudgetTimeService budgetTimeService;

    @PostMapping(value = "CalculateTimeSpan")
    public CalculateTimeSpan.Result calculateTimeSpan(@RequestBody CalculateTimeSpan.Arg arg) {
        return budgetTimeService.calculateTimeSpan(arg);
    }
}
