package com.facishare.crm.fmcg.common.adapter.abstraction;

import com.facishare.paas.metadata.api.IObjectData;

import java.util.List;
import java.util.Set;

public interface IEnterpriseConnectionService {

    Set<String> queryUpstreamTenantIds(String tenantId);

    /**
     * 获取currentTenant在topTenant下的层级， 找到第一个匹配的
     * 从 0 开始计算 ，0层级表示top
     *
     * @param topTenantId
     * @param currentTenantId
     * @return
     */
    Integer getLevel(String topTenantId, String currentTenantId);

    /**
     * 获取currentTenant在topTenant下的层级,且路径需要贴近 containsTenantIds所包含的企业
     *
     * @param topTenantId
     * @param currentTenantId
     * @param containsTenantIds
     * @return
     */
    Integer getLevel(String topTenantId, String currentTenantId, Set<String> containsTenantIds);

    List<String> getClosestTenantList(String topTenantId, String currentTenantId, Set<String> containsTenantIds);

    String getStoreId(String upperTenantId, String downstreamTenantId);

    String getPublicEmployeeIdByContactObj(IObjectData contactObj);

    IObjectData getContactObjByPublicEmployeeId(String tenantId, String publicEmployeeId);

    String getDownloadTenantIdByAccountId(String tenantId, String accountId);
}
