package com.facishare.crm.fmcg.common.utils;

import com.facishare.paas.appframework.core.model.RequestContext;
import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;

import java.util.Objects;

/**
 * Author: wuyx
 * Date: 2024/3/20
 */
@Slf4j
public final class SyncDataUtil {

    private SyncDataUtil() {
    }

    public static boolean isSyncDataAll(RequestContext requestContext) {
        try {
            if (Objects.isNull(requestContext)) {
                return false;
            }
            String xPeerName = requestContext.getX_peerName();
            if (!Strings.isNullOrEmpty(xPeerName)) {
                return xPeerName.contains("fs-sync-data-all");
            }
            String peerName = requestContext.getPeerName();
            return !Strings.isNullOrEmpty(peerName) && peerName.contains("fs-sync-data-all");
        } catch (Exception ex) {
            log.error("is sync data all check error : ", ex);
            return false;
        }
    }
}