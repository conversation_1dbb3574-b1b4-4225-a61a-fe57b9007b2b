package com.facishare.crm.fmcg.common.constant;

/**
 * Author: linmj
 * Date: 2023/9/22 16:10
 */
public interface ScanCodeActionConstants {

    String STORE_SIGN = "STORE_SIGN";

    String RETURN_GOODS = "RETURN_BACK";

    String STORE_STOCK_CHECK = "STORE_STOCK_CHECK";

    String STORE_CHECK_WRITE_OFFS = "STORE_CHECK_WRITE_OFFS";

    String SALES_OUT_OF_WAREHOUSE = "SALES_OUT_OF_WAREHOUSE";

    String EXCHANGE_RETURN_IN = "EXCHANGE_RETURN_IN";

    //大日期
    String CONSUMER_SCAN = "CONSUMER_SCAN_CODE";

    String CONSUMER_GET_RED_PACKAGE = "PRESET_CONSUMER_RED_PACKET";

    String OUTER_CONSUMER_GET_RED_PACKAGE = "PRESET_OUTER_CONSUMER_RED_PACKET";

    String BIG_DATE_ACTIVITY_TYPE_TEMPLATE_ID = "reward.big_date";

    String INTERCONNECT_STORE_BOSS = "6296e9deead62b24377c1441";

    String CODE_LOCK_KEY = "FMCG_CODE_LOCK_KEY:%s";

    String CONSUMER_SCAN_INNER_CODE_ACTIVITY_TYPE_TEMPLATE_ID = "reward.scan_code_get_reward";

    String STOCK_UP_REWARD_TEMPLATE_ID = "reward.stock_up_reward";

    String CONSUMER_SCAN_LOCK_KEY = "CONSUMER_SCAN_LOCK_KEY:%s";

    String SELF_DEFINE_REWARD_TEMPLATE_ID = "reward.self_define_reward";

    String THANKS_YOU_GOODS_ID = "66cc2b3991d2070001f39b13";

    String REWARD_FIXED_ACCOUNT_MODULE = "reward_fixed_account";
}
