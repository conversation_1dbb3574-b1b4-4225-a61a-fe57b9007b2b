package com.facishare.crm.fmcg.common.utils;

import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import com.facishare.paas.appframework.metadata.MetaDataFindService;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SearchTemplateQueryUtil;
import com.google.common.collect.Lists;
import lombok.SneakyThrows;

import java.util.Arrays;
import java.util.List;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2022/4/29 20:13
 */
@SuppressWarnings("Duplicates")
public class QueryDataUtil {

    private QueryDataUtil() {
        // empty ctor
    }

    // one million
    public static final int MAX_QUERY_SIZE = 100000;

    public static SearchTemplateQuery minimumQuery() {
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setOffset(0);
        query.setLimit(-1);
        query.setNeedReturnCountNum(false);
        query.setNeedReturnQuote(false);
        query.setSearchSource("db");
        return query;
    }

    public static SearchTemplateQuery minimumFindOneQuery(IFilter... filters) {
        SearchTemplateQuery query = minimumQuery(filters);
        query.setLimit(1);
        return query;
    }

    public static SearchTemplateQuery minimumFindOneQuery(List<IFilter> filters, List<OrderBy> orders) {
        SearchTemplateQuery query = minimumQuery(filters, orders);
        query.setLimit(1);
        return query;
    }

    public static SearchTemplateQuery minimumQuery(IFilter... filters) {
        SearchTemplateQuery query = minimumQuery();
        query.setFilters(Arrays.stream(filters).collect(Collectors.toList()));
        return query;
    }

    public static SearchTemplateQuery minimumQuery(List<IFilter> filters, List<OrderBy> orders) {
        SearchTemplateQuery query = minimumQuery();
        query.setFilters(filters);
        query.setOrders(orders);
        return query;
    }

    public static SearchTemplateQuery minimumQuery(OrderBy... orders) {
        SearchTemplateQuery query = minimumQuery();
        query.setFilters(Lists.newArrayList());
        query.setOrders(Arrays.stream(orders).collect(Collectors.toList()));
        return query;
    }

    public static List<IObjectData> find(ServiceFacade facade, String tenantId, String apiName, SearchTemplateQuery query, List<String> fields) {
        return find(facade, User.systemUser(tenantId), null, apiName, query, fields);
    }

    public static List<IObjectData> find(ServiceFacade facade, String tenantId, String apiName, SearchTemplateQuery query, List<String> fields, Boolean includeInvalid) {
        return find(facade, User.systemUser(tenantId), apiName, query, fields, includeInvalid);
    }

    public static List<IObjectData> find(ServiceFacade facade, String tenantId, String apiName, SearchTemplateQuery query) {
        return find(facade, User.systemUser(tenantId), null, apiName, query);
    }

    public static List<IObjectData> find(ServiceFacade facade, ActionContext context, String apiName, SearchTemplateQuery query, List<String> fields) {
        return find(facade, User.systemUser(context.getTenantId()), context.getRequestContext(), apiName, query, fields);
    }

    public static List<IObjectData> find(ServiceFacade facade, ControllerContext context, String apiName, SearchTemplateQuery query, List<String> fields) {
        return find(facade, User.systemUser(context.getTenantId()), context.getRequestContext(), apiName, query, fields);
    }

    public static List<IObjectData> find(ServiceFacade facade, User user, RequestContext context, String apiName, SearchTemplateQuery query, List<String> fields) {
        int max = query.getLimit() == 0 || query.getLimit() == -1 ? MAX_QUERY_SIZE : query.getLimit();

        int limit = Math.min(2000, max);
        int offset = 0;

        query.setLimit(limit);
        query.setNeedReturnCountNum(false);

        List<IObjectData> data = Lists.newArrayList();
        QueryResult<IObjectData> result;

        SearchTemplateQuery innerQuery = copy(query);
        while (!(result = facade.findBySearchTemplateQueryWithFields(ActionContextExt.of(user, context).getContext(), apiName, innerQuery, fields)).getData().isEmpty() && data.size() < max) {

            data.addAll(result.getData());
            offset += result.getData().size();

            innerQuery = copy(query);
            innerQuery.setOffset(offset);
        }
        return data;
    }

    public static List<IObjectData> find(ServiceFacade facade, User user, RequestContext context, String apiName, SearchTemplateQuery query) {
        int max = query.getLimit() == 0 || query.getLimit() == -1 ? MAX_QUERY_SIZE : query.getLimit();

        int limit = Math.min(2000, max);
        int offset = 0;

        query.setLimit(limit);
        query.setNeedReturnCountNum(false);

        List<IObjectData> data = Lists.newArrayList();
        QueryResult<IObjectData> result;

        SearchTemplateQuery innerQuery = copy(query);
        while (!(result = facade.findBySearchQuery(ActionContextExt.of(user, context).getContext(), apiName, innerQuery)).getData().isEmpty() && data.size() < max) {

            data.addAll(result.getData());
            offset += result.getData().size();

            innerQuery = copy(query);
            innerQuery.setOffset(offset);
        }
        return data;
    }

    public static List<IObjectData> find(ServiceFacade facade, User user, String apiName, SearchTemplateQuery query, List<String> fields, Boolean includeInvalid) {
        int max = query.getLimit() == 0 || query.getLimit() == -1 ? MAX_QUERY_SIZE : query.getLimit();

        int limit = Math.min(2000, max);
        int offset = 0;

        query.setLimit(limit);
        query.setNeedReturnCountNum(false);

        List<IObjectData> data = Lists.newArrayList();
        QueryResult<IObjectData> result;

        SearchTemplateQuery innerQuery = copy(query);

        MetaDataFindService.QueryContext queryContext = MetaDataFindService.QueryContext.builder()
                .user(user)
                .isSimple(true)
                .skipRelevantTeam(true)
                .includeInvalid(includeInvalid)
                .projectionFields(fields)
                .build();

        while (!(result = facade.findByQueryWithContext(queryContext, apiName, innerQuery)).getData().isEmpty() && data.size() < max) {

            data.addAll(result.getData());
            offset += result.getData().size();

            innerQuery = copy(query);
            innerQuery.setOffset(offset);
        }
        return data;
    }

    public static void findAndConsume(ServiceFacade facade, User user, String apiName, SearchTemplateQuery query, List<String> fields, Consumer<List<IObjectData>> consumer) {
        int max = query.getLimit() == 0 || query.getLimit() == -1 ? MAX_QUERY_SIZE : query.getLimit();

        int limit = Math.min(2000, max);
        int offset = 0;

        query.setLimit(limit);
        query.setNeedReturnCountNum(false);

        int count = 0;
        QueryResult<IObjectData> result;

        SearchTemplateQuery innerQuery = copy(query);
        while (!(result = facade.findBySearchTemplateQueryWithFields(ActionContextExt.of(user, null).getContext(), apiName, innerQuery, fields)).getData().isEmpty() && count < max) {

            consumer.accept(result.getData());
            count += result.getData().size();
            offset += result.getData().size();

            innerQuery = copy(query);
            innerQuery.setOffset(offset);
        }
    }

    @SneakyThrows
    private static SearchTemplateQuery copy(SearchTemplateQuery query) {

        return SearchTemplateQueryUtil.copy(query, true);
    }
}
