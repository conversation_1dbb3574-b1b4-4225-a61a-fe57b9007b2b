package com.facishare.crm.fmcg.fesco.model.apl;

import com.facishare.crm.fmcg.fesco.model.fesco.FescoCommonResult;
import com.fmcg.framework.http.contract.fesco.FescoResult;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

@Data
@ToString
public class AplCommonResult<T extends Serializable> implements Serializable {

    private int code;

    private boolean success;

    private String message;

    private T data;

    public static <T extends Serializable> AplCommonResult<T> convert(FescoCommonResult<T> fescoResult) {
        if (fescoResult.isSuccess()) {
            AplCommonResult<T> result = new AplCommonResult<>();
            result.setSuccess(true);
            result.setData(fescoResult.getData());
            result.setMessage("success");
            return result;
        } else {
            AplCommonResult<T> result = new AplCommonResult<>();
            result.setSuccess(false);
            result.setMessage(fescoResult.getMessage());
            result.setCode(fescoResult.getCode());
            return result;
        }
    }

    public static <T extends Serializable> AplCommonResult<T> success(T data) {
        AplCommonResult<T> result = new AplCommonResult<>();
        result.setSuccess(true);
        result.setData(data);
        result.setMessage("success");
        result.setCode(0);
        return result;
    }

    public static <T extends Serializable> AplCommonResult<T> failed(FescoResult.Result remoteResult) {
        AplCommonResult<T> result = new AplCommonResult<>();
        result.setSuccess(remoteResult.isSuccess());
        result.setMessage(remoteResult.getMessage());
        result.setCode(remoteResult.getCode());
        return result;
    }

    public static <T extends Serializable> AplCommonResult<T> exception(Exception ex) {
        AplCommonResult<T> result = new AplCommonResult<>();
        result.setSuccess(false);
        result.setMessage(ex.getMessage());
        result.setCode(500001);
        return result;
    }
}
