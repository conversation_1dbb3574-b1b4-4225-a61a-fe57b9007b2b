package com.facishare.crm.fmcg.dms.web.manager;

import com.facishare.crm.fmcg.common.apiname.AccountsPayableNoteFields;
import com.facishare.crm.fmcg.dms.i18n.I18NKeys;
import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.common.apiname.PayFields;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import de.lab4inf.math.util.Strings;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.facishare.crm.fmcg.dms.web.manager.AccountsPayableCommonManager.CHECK_MATCH_DATA;

@Service
@Slf4j
public class CommonAccountsReceivableNoteManager {

    @Autowired
    private ServiceFacade serviceFacade;

    public boolean checkPayMatchNote(User user, IObjectData pay) {
        if (Objects.isNull(pay)) {
            throw new ValidateException(I18N.text(I18NKeys.FMCG_ACCOUNTS_PAYABLE_VALIDATE_NO_PAY));
        }
        List<Map> checkMatchData = pay.get(CHECK_MATCH_DATA, List.class);
        if (CollectionUtils.empty(checkMatchData)) {
            return false;
        }
        BigDecimal noMatchAmount = pay.get(PayFields.NO_MATCH_AMOUNT, BigDecimal.class, BigDecimal.ZERO);
        String payType = pay.get(PayFields.PAY_TYPE, String.class);

        if (noMatchAmount.compareTo(BigDecimal.ZERO) == 0) {
            throw new ValidateException(I18N.text(I18NKeys.FMCG_ACCOUNTS_PAYABLE_VALIDATE_PAY_NO_MATCH_AMOUNT_NOT_ZERO));
        }
        if (Strings.isNullOrEmpty(payType)) {
            throw new ValidateException(I18N.text(I18NKeys.FMCG_ACCOUNTS_PAYABLE_VALIDATE_PAY_PAY_TYPE_NOT_EMPTY));
        }
        boolean isRedPay = Objects.equals(payType, PayFields.PAY_TYPE__RED);
        List<String> dataIds = checkMatchData.stream().map(x -> x.get("_id").toString()).collect(Collectors.toList());
        if (CollectionUtils.empty(dataIds)) {
            return false;
        }
        List<IObjectData> accountsPayable = serviceFacade.findObjectDataByIds(user.getTenantId(), dataIds, ApiNames.ACCOUNTS_PAYABLE_NOTE_OBJ);
        if (CollectionUtils.empty(accountsPayable)) {
            return false;
        }
        accountsPayable.forEach(data -> {
            checkMatchData.stream()
                    .filter(argData -> Objects.equals(data.getId(), argData.get("_id").toString()))
                    .findFirst()
                    .ifPresent(argData -> data.set(AccountsPayableNoteFields.VIR_THIS_MATCH_AMOUNT, argData.get(AccountsPayableNoteFields.VIR_THIS_MATCH_AMOUNT)));
        });

        BigDecimal accountsPayableAmount = accountsPayable.stream()
                .map(x -> x.get(AccountsPayableNoteFields.VIR_THIS_MATCH_AMOUNT, BigDecimal.class, BigDecimal.ZERO))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        if (isRedPay) {
            if (accountsPayableAmount.compareTo(BigDecimal.ZERO) > 0 || accountsPayableAmount.compareTo(noMatchAmount) < 0) {
                throw new ValidateException(I18N.text(I18NKeys.COMMON_ACCOUNTS_RECEIVABLE_NOTE_MANAGER_0));
            }
        } else {
            if (accountsPayableAmount.compareTo(BigDecimal.ZERO) < 0 || accountsPayableAmount.compareTo(noMatchAmount) > 0) {
                throw new ValidateException(I18N.text(I18NKeys.COMMON_ACCOUNTS_RECEIVABLE_NOTE_MANAGER_1));
            }
        }
        for (IObjectData arData : accountsPayable) {
            BigDecimal noSettledAmount = arData.get(AccountsPayableNoteFields.PRE_MATCH_AMOUNT, BigDecimal.class, BigDecimal.ZERO);
            BigDecimal virThisMatchAmount = arData.get(AccountsPayableNoteFields.VIR_THIS_MATCH_AMOUNT, BigDecimal.class, BigDecimal.ZERO);
            if (noSettledAmount.multiply(virThisMatchAmount).compareTo(BigDecimal.ZERO) < 0
                    || virThisMatchAmount.abs().compareTo(noSettledAmount.abs()) > 0
                    || virThisMatchAmount.compareTo(BigDecimal.ZERO) == 0) {
                throw new ValidateException(String.format(I18N.text(I18NKeys.COMMON_ACCOUNTS_RECEIVABLE_NOTE_MANAGER_2), arData.getName()));
            }
        }
        return true;
    }
}
