package com.facishare.crm.fmcg.dms.service.mapper;

import com.facishare.crm.fmcg.common.apiname.*;
import com.facishare.crm.fmcg.common.gray.TPMGrayUtils;
import com.facishare.crm.fmcg.common.utils.QueryDataUtil;
import com.facishare.crm.fmcg.dms.errors.AbandonActionException;
import com.facishare.crm.fmcg.dms.model.FinancialBill;
import com.facishare.crm.fmcg.dms.service.abastraction.MatchableBillMapService;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.fmcg.framework.http.contract.sales.QueryRelatedOrder;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Component
@Slf4j
@SuppressWarnings("Duplicates")
public class ReceivableMatchableBillMapService extends MatchableBillMapService {

    @Override
    protected void beforeMap(FinancialBill bill) {
        if (Objects.isNull(bill.getData())) {
            bill.setData(serviceFacade.findObjectData(User.systemUser(bill.getTenantId()), bill.getId(), ApiNames.ACCOUNTS_RECEIVABLE_NOTE_OBJ));
        }
        if (CollectionUtils.isEmpty(bill.getDetails())) {
            bill.setDetails(queryReceivableNoteDetails(bill.getTenantId(), bill.getData().getId()));
        }
    }

    @Override
    protected void validate(FinancialBill bill) {
        if (Strings.isNullOrEmpty(bill.getData().get(AccountsReceivableNoteFields.ACCOUNT_ID, String.class))) {
            throw new AbandonActionException("account receivable bill account id cat not be null or empty.");
        }
        if (Objects.equals(Boolean.TRUE, bill.getData().get(AccountsReceivableNoteFields.OPENING_BALANCE, Boolean.class))) {
            throw new AbandonActionException("opening balance receivable abandon.");
        }
        if (CollectionUtils.isEmpty(bill.getDetails())) {
            throw new AbandonActionException("account receivable bill details cat not be null or empty.");
        }
    }

    @Override
    protected List<FinancialBill> mapToMatchableBills(FinancialBill bill) {
        if (isBlueReceivable(bill)) {
            return Lists.newArrayList(bill);
        } else if (isRedReceivable(bill)) {
            List<FinancialBill> receivables;

            IObjectData receivable = serviceFacade.findObjectData(User.systemUser(bill.getTenantId()), bill.getId(), bill.getApiName());
            String receivableType = receivable.get(AccountsReceivableNoteFields.OBJECT_RECEIVABLE, String.class);
            String returnGoodsInvoiceRecordType = "";
            if (ApiNames.GOODS_RECEIVED_NOTE_OBJ.equals(receivableType)) {
                IObjectData data = serviceFacade.findObjectData(User.systemUser(bill.getTenantId()),
                        receivable.get(AccountsReceivableNoteFields.OBJECT_RECEIVABLE_DATA_ID, String.class),
                        ApiNames.RETURNED_GOODS_INVOICE_OBJ);
                if (Objects.nonNull(data)) {
                    returnGoodsInvoiceRecordType = data.getRecordType();
                }
            }
            if (AccountsReceivableNoteFields.OUTBOUND_DELIVERY_NOTE_OBJ.equals(receivableType) || ReturnedGoodsInvoiceFields.EXCHANGE_RECORD.equals(returnGoodsInvoiceRecordType)) {
                //换货
                receivables = querySwapOutReceivableNoteByReturnGoodsInvoiceId(bill.getTenantId(), receivable.get(AccountsReceivableNoteFields.OBJECT_RECEIVABLE_DATA_ID, String.class), ReturnedGoodsInvoiceProductFields.SWAP_OUT);
                receivables.add(bill);
            } else {
                List<String> orderIds = querySalesOrderIdsByReceivable(bill);

                receivables = convertToAccountsReceivableNote(bill, findReceivablesBySalesOrderIds(bill.getTenantId(), orderIds));
                receivables.add(bill);
            }

            return receivables;
        } else {
            return Lists.newArrayList();
        }
    }

    private boolean isBlueReceivable(FinancialBill receivable) {
        // 没有任何负金额明细的应收就是蓝应收
        return receivable.getDetails()
                .stream()
                .allMatch(detail ->
                        detail.get(AccountsReceivableDetailFields.PRICE_TAX_AMOUNT, BigDecimal.class).compareTo(BigDecimal.ZERO) >= 0
                );
    }

    private boolean isRedReceivable(FinancialBill receivable) {
        // 没有任何负金额明细的应收就是蓝应收
        return receivable.getDetails()
                .stream()
                .allMatch(detail ->
                        detail.get(AccountsReceivableDetailFields.PRICE_TAX_AMOUNT, BigDecimal.class).compareTo(BigDecimal.ZERO) <= 0
                );
    }


    private List<IObjectData> queryReceivableNoteDetails(String tenantId, String id) {
        IFilter idFilter = new Filter();
        idFilter.setFieldName(AccountsReceivableDetailFields.AR_ID);
        idFilter.setOperator(Operator.EQ);
        idFilter.setFieldValues(Lists.newArrayList(id));

        SearchTemplateQuery stq = QueryDataUtil.minimumQuery(idFilter);

        return QueryDataUtil.find(
                serviceFacade,
                tenantId,
                ApiNames.ACCOUNTS_RECEIVABLE_DETAIL_OBJ,
                stq,
                Lists.newArrayList(
                        CommonFields.ID,
                        CommonFields.NAME,
                        CommonFields.OWNER,
                        CommonFields.CREATE_BY,
                        CommonFields.CREATE_TIME,
                        AccountsReceivableDetailFields.PRICE_TAX_AMOUNT,
                        AccountsReceivableDetailFields.AR_ID,
                        AccountsReceivableDetailFields.ORDER_ID,
                        AccountsReceivableDetailFields.ORDER_PRODUCT_ID,
                        AccountsReceivableDetailFields.DELIVERY_NOTE_ID,
                        AccountsReceivableDetailFields.DELIVERY_NOTE_PRODUCT_ID,
                        AccountsReceivableDetailFields.GOODS_RECEIVED_NOTE_ID,
                        AccountsReceivableDetailFields.GOODS_RECEIVED_NOTE_PRODUCT_ID,
                        AccountsReceivableDetailFields.RECEIVABLE_OBJECT_DATA_ID,
                        AccountsReceivableDetailFields.RECEIVABLE_OBJECT_API_NAME,
                        AccountsReceivableDetailFields.RECEIVABLE_OBJECT_DETAIL_DATA_ID,
                        AccountsReceivableDetailFields.RECEIVABLE_OBJECT_DETAIL_API_NAME,
                        AccountsReceivableDetailFields.UNIT,
                        AccountsReceivableDetailFields.AR_QUANTITY,
                        AccountsReceivableDetailFields.SKU_ID,
                        AccountsReceivableDetailFields.TAX_PRICE,
                        AccountsReceivableDetailFields.SOURCE_API_NAME,
                        AccountsReceivableDetailFields.SOURCE_DATA_ID,
                        AccountsReceivableDetailFields.SOURCE_DETAIL_DATA_ID,
                        AccountsReceivableDetailFields.SOURCE_DETAIL_API_NAME
                )
        );
    }

    private List<String> querySalesOrderIdsByReceivable(FinancialBill bill) {
        String objectReceivable = bill.getData().get(AccountsReceivableNoteFields.OBJECT_RECEIVABLE, String.class);
        if (Strings.isNullOrEmpty(objectReceivable)) {
            throw new AbandonActionException("object receivable not support.");
        }
        switch (objectReceivable) {
            case ApiNames.GOODS_RECEIVED_NOTE_OBJ:
                return findSalesOrderIdsFromGoodsReceivedNotes(bill);
            case ApiNames.DELIVERY_NOTE_OBJ:
                return findSalesOrderIdsFromDeliveryNotes(bill);
            default:
                throw new AbandonActionException("object receivable not support.");
        }
    }

    private List<String> findSalesOrderIdsFromDeliveryNotes(FinancialBill bill) {
        String fieldName;
        if (!TPMGrayUtils.accountReceivableDetailUseWhatField(bill.getTenantId())) {
            fieldName = AccountsReceivableDetailFields.DELIVERY_NOTE_ID;
        } else {
            fieldName = AccountsReceivableDetailFields.RECEIVABLE_OBJECT_DATA_ID;
        }
        List<String> deliveryNoteIds = bill.getDetails()
                .stream()
                .map(m -> m.get(fieldName, String.class))
                .filter(f -> !Strings.isNullOrEmpty(f))
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(deliveryNoteIds)) {
            return Lists.newArrayList();
        }

        List<IObjectData> deliveryNotes = serviceFacade.findObjectDataByIdsIgnoreAll(bill.getTenantId(), deliveryNoteIds, ApiNames.DELIVERY_NOTE_OBJ);
        if (CollectionUtils.isEmpty(deliveryNotes)) {
            return Lists.newArrayList();
        }

        return deliveryNotes.stream()
                .map(m -> m.get(DeliveryNoteFields.SALES_ORDER_ID, String.class))
                .filter(f -> !Strings.isNullOrEmpty(f))
                .distinct()
                .collect(Collectors.toList());
    }

    private List<String> findSalesOrderIdsFromGoodsReceivedNotes(FinancialBill bill) {
        String fieldName;
        if (!TPMGrayUtils.accountReceivableDetailUseWhatField(bill.getTenantId())) {
            fieldName = AccountsReceivableDetailFields.GOODS_RECEIVED_NOTE_ID;
        } else {
            fieldName = AccountsReceivableDetailFields.RECEIVABLE_OBJECT_DATA_ID;
        }

        List<String> goodsReceivedNoteIds = bill.getDetails().stream()
                .map(m -> m.get(fieldName, String.class))
                .filter(f -> !Strings.isNullOrEmpty(f))
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(goodsReceivedNoteIds)) {
            return Lists.newArrayList();
        }

        List<IObjectData> goodsReceivedNotes = serviceFacade.findObjectDataByIdsIgnoreAll(bill.getTenantId(), goodsReceivedNoteIds, ApiNames.GOODS_RECEIVED_NOTE_OBJ);
        if (CollectionUtils.isEmpty(goodsReceivedNotes)) {
            return Lists.newArrayList();
        }

        List<String> returnedNoteIds = goodsReceivedNotes.stream()
                .map(m -> m.get(GoodsReceivedNoteFields.RETURN_NOTE_ID, String.class))
                .filter(f -> !Strings.isNullOrEmpty(f))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(returnedNoteIds)) {
            return Lists.newArrayList();
        }

        List<IObjectData> returnedNotes = serviceFacade.findObjectDataByIdsIgnoreAll(bill.getTenantId(), returnedNoteIds, ApiNames.RETURNED_GOODS_INVOICE_OBJ);
        List<String> salesOrderIds = returnedNotes.stream()
                .map(returnedNote -> returnedNote.get(ReturnedGoodsInvoiceFields.ORDER_ID, String.class))
                .filter(f -> !Strings.isNullOrEmpty(f))
                .collect(Collectors.toList());

        List<IObjectData> returnedNoteDetails = queryReturnedNoteDetails(bill.getTenantId(), returnedNoteIds);
        for (IObjectData returnedNoteDetail : returnedNoteDetails) {
            String innerOrderId = returnedNoteDetail.get(ReturnedGoodsInvoiceProductFields.ORDER_ID, String.class);
            if (!Strings.isNullOrEmpty(innerOrderId)) {
                salesOrderIds.add(innerOrderId);
            }
        }

        for (String returnedNoteId : returnedNoteIds) {
            int tenantIdInt = Integer.parseInt(bill.getTenantId());

            QueryRelatedOrder.Arg arg = new QueryRelatedOrder.Arg();
            arg.setTenantId(tenantIdInt);
            arg.setApiName(ApiNames.RETURNED_GOODS_INVOICE_OBJ);
            arg.setDataId(returnedNoteId);
            arg.setRelatedApiName(ApiNames.SALES_ORDER_OBJ);

            QueryRelatedOrder.Result relatedOrderResult = fmcgSalesProxy.queryRelatedOrder(tenantIdInt, -10000, arg);

            if (ApiNames.SALES_ORDER_OBJ.equals(relatedOrderResult.getRelatedApiName()) && CollectionUtils.isNotEmpty(relatedOrderResult.getDataIdList())) {
                salesOrderIds.addAll(relatedOrderResult.getDataIdList());
            }
        }

        return salesOrderIds.stream().filter(id -> !Strings.isNullOrEmpty(id)).distinct().collect(Collectors.toList());
    }

    private List<IObjectData> queryReturnedNoteDetails(String tenantId, List<String> returnedNoteIds) {
        IFilter idFilter = new Filter();
        idFilter.setFieldName(ReturnedGoodsInvoiceProductFields.RETURNED_GOODS_INV_ID);
        idFilter.setOperator(Operator.IN);
        idFilter.setFieldValues(returnedNoteIds);

        SearchTemplateQuery stq = QueryDataUtil.minimumQuery(idFilter);

        return QueryDataUtil.find(
                serviceFacade,
                tenantId,
                ApiNames.RETURNED_GOODS_INVOICE_PRODUCT_OBJ,
                stq,
                Lists.newArrayList(
                        CommonFields.ID,
                        ReturnedGoodsInvoiceProductFields.ORDER_ID
                )
        );
    }
}