package com.facishare.crm.fmcg.dms.service.abastraction;

import com.facishare.crm.fmcg.common.gray.TPMGrayUtils;
import com.facishare.crm.fmcg.dms.business.enums.AccountPayableSwitchEnum;
import com.facishare.crm.fmcg.dms.constants.AccountsReceivableConstants;
import com.facishare.crm.fmcg.dms.errors.AbandonActionException;
import com.facishare.crm.fmcg.dms.errors.RetryActionException;
import com.facishare.crm.fmcg.dms.i18n.I18NKeys;
import com.facishare.crm.fmcg.dms.model.FinancialBill;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.StopWatch;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.restful.client.exception.FRestClientException;
import com.fxiaoke.bizconf.arg.QueryConfigByRankArg;
import com.fxiaoke.bizconf.bean.Rank;
import com.fxiaoke.bizconf.factory.BizConfClient;
import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

@Slf4j
@SuppressWarnings("Duplicates")
public abstract class AutoMatchService implements IAutoMatchService {

    protected static final long LOCK_WAIT = 20;
    protected static final long LOCK_LEASE = 60;

    @Resource
    protected RedissonClient redissonCmd;
    @Resource
    protected ServiceFacade serviceFacade;
    @Resource
    protected BizConfClient bizConfClient;

    protected abstract void beforeMatch(FinancialBill bill);

    /**
     * validate financial bill
     *
     * @param bill financial bill
     */
    protected abstract void validate(FinancialBill bill);

    /**
     * produce match note data by financial bill
     *
     * @param bill financial bill
     */
    protected abstract void doMatch(FinancialBill bill);

    /**
     * build financial bill lock key for distributed lock
     *
     * @param bill financial bill
     */
    protected abstract String buildFinancialBillLockKey(FinancialBill bill);

    protected abstract boolean denyAutoMatch(String tenantId);

    /**
     * do match in transactional
     *
     * @param bill financial bill
     */
    @Override
    public void match(FinancialBill bill) {
        log.info("match started : {}", bill);

        StopWatch watch = StopWatch.create("FINANCIAL_BILL_MATCH." + bill.getApiName() + "." + bill.getId());
        //判断是否是应收的老企业，老企业不处理逻辑
        if (TPMGrayUtils.accountReceivableOldTenant(bill.getTenantId())) {
            log.info("match：account receivable old tenant,tenantId:{}", bill.getTenantId());
            return;
        }
        // 判断是否开启了明细应收核销
        if (denyAutoMatch(bill.getTenantId())) {
            return;
        }
        //判断是否开启自动核销开关
        if (!enableAccountsReceivableAutoMatchButton(bill.getTenantId())) {
            log.info("not enableAccountsReceivableAutoMatchButton,tenantId:{}", bill.getTenantId());
            return;
        }
        // 填充必要的单据信息
        beforeMatch(bill);
        watch.lap("beforeMatch");

        // 锁定当前核销操作，保证指定维度下只有单次核销操作在进行
        if (this.tryLock(bill)) {
            watch.lap("lock");
            try {
                // 校验业务单据
                this.validate(bill);
                watch.lap("validate");

                // 根据业务单据信息生成核销单信息，以及当前单据需要的业务单据回写信息
                this.doMatch(bill);
                watch.lap("produce");
            } catch (AbandonActionException ex) {
                log.warn("financial bill match abandon : ", ex);
            } catch (RetryActionException ex) {
                log.warn("financial bill match cause retry exception : ", ex);
                throw ex;
            } catch (Exception ex) {
                log.error("financial bill match cause unknown exception : ", ex);
                throw ex;
            } finally {
                this.unlock(bill);
                watch.lap("unlock");
            }
        } else {
            throw new RetryActionException("try lock financial bill fail");
        }

        watch.logSlow(500);
    }

    /**
     * unlock bill by financial bill key
     *
     * @param bill financial bill
     */
    private void unlock(FinancialBill bill) {
        String key = buildFinancialBillLockKey(bill);
        RLock lock = redissonCmd.getLock(key);

        log.info("unlock financial bill : {}", key);

        if (lock.isLocked() && lock.isHeldByCurrentThread()) {
            lock.unlock();
        }
    }

    /**
     * try lock bill by financial bill key
     *
     * @param bill financial bill
     */
    private boolean tryLock(FinancialBill bill) {
        String key = buildFinancialBillLockKey(bill);
        RLock lock = redissonCmd.getLock(key);

        log.info("try lock financial bill : {}", key);

        if (lock.isLocked() && lock.isHeldByCurrentThread()) {
            return true;
        }
        try {
            return lock.tryLock(LOCK_WAIT, LOCK_LEASE, TimeUnit.SECONDS);
        } catch (InterruptedException ex) {
            Thread.currentThread().interrupt();
            throw new MetaDataBusinessException(String.format("try lock financial bill cause thread interrupted exception : %s", key));
        }
    }

    private boolean enableAccountsReceivableAutoMatchButton(String tenantId) {
        QueryConfigByRankArg queryStatusArg =
                QueryConfigByRankArg.builder().rank(Rank.TENANT).key(AccountsReceivableConstants.ACCOUNTS_RECEIVABLE_AUTO_MATCH_BUTTON_SWITCH_KEY).tenantId(tenantId).pkg(AccountsReceivableConstants.SWITCH_PKG).build();
        String config;
        try {
            config = bizConfClient.queryConfigByRank(queryStatusArg);
        } catch (FRestClientException ex) {
            throw new ValidateException(I18N.text(I18NKeys.ACCOUNTS_PAYABLE_QUERY_ERROR));
        }
        if (Strings.isNullOrEmpty(config)) {
            return true;
        }
        int value = Integer.parseInt(config);
        return value == AccountPayableSwitchEnum.OPENED.getStatus();
    }
}