package com.facishare.crm.fmcg.dms.controller;

import com.facishare.crm.fmcg.common.apiname.EnterpriseFundAccountObjFields;
import com.facishare.paas.appframework.core.predef.controller.StandardDescribeLayoutController;
import com.facishare.paas.appframework.metadata.FormComponentExt;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.metadata.ui.layout.IFieldSection;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Objects;
import java.util.Set;


@Slf4j
@SuppressWarnings("Duplicates")
public class EnterpriseFundAccountObjDescribeLayoutController extends StandardDescribeLayoutController {
    private static final Set<String> REQUIRED_FIELD_API_NAME = Sets.newHashSet();

    static {

        REQUIRED_FIELD_API_NAME.add(EnterpriseFundAccountObjFields.ACCOUNT_TYPE);
        REQUIRED_FIELD_API_NAME.add(EnterpriseFundAccountObjFields.BASE_AMOUNT);
    }

    @Override
    protected Result after(Arg arg, Result result) {
        if ("edit".equals(arg.getLayout_type()) && Boolean.TRUE.equals(arg.getInclude_layout())) {
            try {

                LayoutExt layout = LayoutExt.of(result.getLayout().toLayout());
                FormComponentExt form = layout.getFormComponent().orElse(null);
                if (!Objects.isNull(form)) {
                    List<IFieldSection> sections = form.getFieldSections();
                    for (IFieldSection section : sections) {
                        section.getFields().forEach(field -> {
                            if (REQUIRED_FIELD_API_NAME.contains(field.getFieldName())) {
                                field.setRequired(true);
                            }

                        });
                    }
                }
            } catch (Exception ex) {
                log.info("override EnterpriseFundAccountObj layout cause unknown exception  : ", ex);
            }
        }


        return super.after(arg, result);
    }
}
