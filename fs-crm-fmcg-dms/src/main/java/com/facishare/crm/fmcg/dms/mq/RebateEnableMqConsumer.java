package com.facishare.crm.fmcg.dms.mq;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.common.gray.TPMGrayUtils;
import com.facishare.crm.fmcg.dms.business.DMSScriptHandlerFactory;
import com.facishare.crm.fmcg.dms.business.abstraction.IDMSScriptHandler;
import com.facishare.crm.fmcg.dms.business.enums.ScriptHandlerNameEnum;
import com.facishare.crm.fmcg.dms.mq.model.ModuleCtrlMessage;
import com.facishare.crm.fmcg.dms.service.abastraction.PluginInstanceService;
import com.facishare.crm.fmcg.dms.web.abstraction.IAccountsReceivableNoteService;
import com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer;
import com.github.trace.TraceContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.common.message.MessageExt;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import javax.annotation.Resource;
import java.util.Objects;
import java.util.UUID;


@Slf4j
@Component
public class RebateEnableMqConsumer {

    public static final String ACCOUNTS_RECEIVABLE_REL_REBATE_PLUGIN_NAME = "accounts_receivable_rel_rebate";
    private static final Logger logger = LoggerFactory.getLogger(RebateEnableMqConsumer.class);
    private static final String CONFIG_NAME = "rocketmq-consumer.ini";
    private static final String SECTION_NAME = "common,name_server_02,module_ctrl_init_fmcg_dms";
    private AutoConfMQPushConsumer processor;
    @Resource
    private DMSScriptHandlerFactory dmsScriptHandlerFactory;
    @Resource
    private IAccountsReceivableNoteService receivableNoteService;
    @Resource
    private PluginInstanceService pluginInstanceService;

    @PostConstruct
    public void init() {
        if (!Objects.equals("1", System.getProperty("mn.dms.flag"))) {
            return;
        }
        logger.info("RebateEnableMqConsumer start init.");
        MessageListenerConcurrently listener = (messages, context) -> {
            for (MessageExt messageExt : messages) {
                try {
                    TraceContext.get().setTraceId(UUID.randomUUID().toString());
                    process(messageExt);
                } catch (Exception ex) {
                    logger.error("[RebateEnableMqConsumer consumer error :" + messages + "]", ex);
                    return ConsumeConcurrentlyStatus.RECONSUME_LATER;
                } finally {
                    TraceContext.remove();
                }
            }
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        };
        try {
            processor = new AutoConfMQPushConsumer(CONFIG_NAME, SECTION_NAME, listener);
            processor.start();
        } catch (Exception e) {
            logger.error("init RebateEnableMqConsumer mq consumer failed.", e);
        }
    }

    private void process(MessageExt messageExt) {
        ModuleCtrlMessage moduleCtrlMessage = JSON.parseObject(messageExt.getBody(), ModuleCtrlMessage.class);
        logger.info("RebateEnableMqConsumer consume start.TenantId:{},moduleCode:{}", moduleCtrlMessage.getTenantId(), moduleCtrlMessage.getModuleCode());
        if (!Objects.equals(moduleCtrlMessage.getModuleCode(), "rebate")) {
            return;
        }

        if (!TPMGrayUtils.dmsOpen1_1ScriptHandlerAuth(moduleCtrlMessage.getTenantId())) {
            return;
        }

        if (receivableNoteService.denyAccountsReceivableEnable(moduleCtrlMessage.getTenantId())) {
            return;
        }

        IDMSScriptHandler onlyRebateEnableHandler = dmsScriptHandlerFactory.resolve(ScriptHandlerNameEnum.REBATE_ENABLE.getHandlerName());
        onlyRebateEnableHandler.initButton(moduleCtrlMessage.getTenantId());

        pluginInstanceService.addPluginUnit(Integer.parseInt(moduleCtrlMessage.getTenantId()), -10000, ApiNames.ACCOUNTS_RECEIVABLE_NOTE_OBJ, ACCOUNTS_RECEIVABLE_REL_REBATE_PLUGIN_NAME);
    }

    @PreDestroy
    public void shutDown() {
        processor.close();
    }
}