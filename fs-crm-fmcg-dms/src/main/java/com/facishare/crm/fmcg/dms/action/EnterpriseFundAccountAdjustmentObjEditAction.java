package com.facishare.crm.fmcg.dms.action;

import com.facishare.crm.fmcg.common.apiname.EnterpriseFundAccountAdjustmentFields;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.predef.action.StandardAddAction;
import com.facishare.paas.metadata.api.IObjectData;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;


@Slf4j
public class EnterpriseFundAccountAdjustmentObjEditAction extends StandardAddAction {

    @Override
    protected void before(Arg arg) {
        super.before(arg);

        validateAmount();
    }

    private void validateAmount() {
        IObjectData data = arg.getObjectData().toObjectData();

        if (EnterpriseFundAccountAdjustmentFields.RecordTypeApiName.REVENUE.equals(data.getRecordType())) {
            BigDecimal revenueAmount = data.get(EnterpriseFundAccountAdjustmentFields.REVENUE_AMOUNT, BigDecimal.class);
            if (revenueAmount == null || revenueAmount.compareTo(BigDecimal.ZERO) <= 0) {
                throw new ValidateException("revenue_amount is not valid");
            }
        } else if (EnterpriseFundAccountAdjustmentFields.RecordTypeApiName.EXPENDITURE.equals(data.getRecordType())) {
            BigDecimal expenseAmount = data.get(EnterpriseFundAccountAdjustmentFields.EXPENSE_AMOUNT, BigDecimal.class);
            if (expenseAmount == null || expenseAmount.compareTo(BigDecimal.ZERO) <= 0) {
                throw new ValidateException("expense_amount is not valid");
            }
        } else if (EnterpriseFundAccountAdjustmentFields.RecordTypeApiName.TRANSFER.equals(data.getRecordType())) {
            BigDecimal transferAmount = data.get(EnterpriseFundAccountAdjustmentFields.TRANSFER_AMOUNT, BigDecimal.class);
            if (transferAmount == null || transferAmount.compareTo(BigDecimal.ZERO) <= 0) {
                throw new ValidateException("transfer_amount is not valid");
            }
        } else {
            throw new ValidateException("RecordType is not valid");
        }

    }

}
