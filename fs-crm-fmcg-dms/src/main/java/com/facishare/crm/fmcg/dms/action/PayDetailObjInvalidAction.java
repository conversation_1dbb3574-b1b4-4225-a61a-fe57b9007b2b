package com.facishare.crm.fmcg.dms.action;

import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.dms.i18n.I18NKeys;
import com.facishare.paas.I18N;
import com.facishare.crm.fmcg.common.apiname.CommonFields;
import com.facishare.crm.fmcg.common.apiname.MatchNoteDetailFields;
import com.facishare.crm.fmcg.common.utils.QueryDataUtil;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.predef.action.StandardInvalidAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;

@SuppressWarnings("all")
public class PayDetailObjInvalidAction extends StandardInvalidAction {

    @Override
    protected void before(Arg arg) {
        super.before(arg);

        validateExistsMatchNoteDetail();
    }

    private void validateExistsMatchNoteDetail() {
        if (existsMatchNoteDetail()) {
            throw new ValidateException(I18N.text(I18NKeys.PAY_DETAIL_OBJ_INVALID_ACTION_0));
        }
    }

    private boolean existsMatchNoteDetail() {


        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setOffset(0);
        query.setLimit(1);
        query.setNeedReturnCountNum(false);
        query.setNeedReturnQuote(false);
        query.setSearchSource("db");

        IFilter creditDataIdFilter = new Filter();
        creditDataIdFilter.setFieldName(MatchNoteDetailFields.CREDIT_DETAIL_DATA_ID);
        creditDataIdFilter.setOperator(Operator.EQ);
        creditDataIdFilter.setFieldValues(Lists.newArrayList(arg.getObjectDataId()));

        IFilter creditApiNameFilter = new Filter();
        creditApiNameFilter.setFieldName(MatchNoteDetailFields.CREDIT_DETAIL_API_NAME);
        creditApiNameFilter.setOperator(Operator.EQ);
        creditApiNameFilter.setFieldValues(Lists.newArrayList(ApiNames.PAY_DETAIL_OBJ));

        query.setFilters(Lists.newArrayList(creditDataIdFilter, creditApiNameFilter));

        List<IObjectData> result = QueryDataUtil.find(
                serviceFacade,
                actionContext.getTenantId(),
                ApiNames.MATCH_NOTE_DETAIL_OBJ,
                query,
                Lists.newArrayList(
                        CommonFields.ID
                )
        );
        return !CollectionUtils.isEmpty(result);
    }
}
